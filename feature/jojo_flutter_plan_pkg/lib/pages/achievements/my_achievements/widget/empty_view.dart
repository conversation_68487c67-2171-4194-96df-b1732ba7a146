import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';

import '../../../../common/config/config.dart';
import '../../../../generated/l10n.dart';
import '../../../../static/img.dart';

class EmptyView extends StatelessWidget {
  const EmptyView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 370.rdp + 150.rdp,
      child: Stack(
        children: [
          Container(
            height: 370.rdp,
            width: double.infinity,
            color: Colors.yellow,
            child: Image.asset(
              AssetsImg.MEDALS_LATEST_MEDALS_MEDIUM_BG,
              package: Config.package,
              height: 370.rdp,
              fit: BoxFit.fill,
              centerSlice: const Rect.fromLTRB(113.0, 10.0, 115.0, 360.0),
            ),
          ),
          Positioned(
            top: 292.rdp,
            left: 0,
            right: 0,
              child: Column(
            children: [
              ImageAssetWeb(
                assetName: AssetsImg.MEDALS_MEDAL_EMPTY,
                package: Config.package,
                height: 200.rdp,
              ),
              SizedBox(
                height: 4.rdp,
              ),
              Text(S.of(context).noAchievementsYet,
                  style: context.textstyles.bodyText.pf.copyWith(
                    color: context.appColors.jColorGray4,
                  ))
            ],
          ))
        ],
      ),
    );
  }
}
