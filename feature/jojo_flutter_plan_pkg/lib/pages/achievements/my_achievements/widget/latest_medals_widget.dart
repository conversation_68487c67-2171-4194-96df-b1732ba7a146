import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';

import '../../../../common/config/config.dart';
import '../../../../static/img.dart';
import '../../model/medal_model.dart';
import '../controller.dart';
import '../state.dart';
import '../viewmodel/card_controller.dart';
import '../viewmodel/card_state.dart';
import '../viewmodel/view_model.dart';
import '../../widget/achievement_card_wapper.dart';
import 'carousel_indicator.dart';

class LatestMedalsWidget extends StatefulWidget {
  final List<MedalModel> latestMedals;
  final int defaultSelectIndex;
  final Function(MedalModel medal) onItemTap;
  final Function(int index, MedalModel medal) onSelectedItem;
  final String? subjectColor;

  const LatestMedalsWidget(
    this.latestMedals, {
    super.key,
    required this.onItemTap,
    required this.onSelectedItem,
    this.defaultSelectIndex = 0,
    this.subjectColor,
  });

  @override
  State<StatefulWidget> createState() => LatestMedalsWidgetState();
}

class LatestMedalsWidgetState extends State<LatestMedalsWidget> {
  late PageController _pageController;
  late ValueNotifier<int> _indicatorController;

  MyAchievementsState get _state =>
      context.read<MyAchievementsController>().state;

  @override
  void initState() {
    super.initState();
    _indicatorController = ValueNotifier<int>(widget.defaultSelectIndex);

    _pageController = PageController(initialPage: widget.defaultSelectIndex);
    _pageController.addListener(() {
      final double? page = _pageController.page;
      if (page == null) return;

      int newPage = page.round();
      if ((page - newPage).abs() < 0.1 &&
          newPage != _indicatorController.value) {
        _indicatorController.value = newPage;

        // 通知选中项
        widget.onSelectedItem(newPage, widget.latestMedals[newPage]);
      }
    });

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.latestMedals.isNotEmpty) {
        // 通知选中项
        widget.onSelectedItem(widget.defaultSelectIndex,
            widget.latestMedals[widget.defaultSelectIndex]);
      }
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    _indicatorController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 370.rdp,
      child: Stack(children: [
        ImageAssetWeb(
          assetName: AssetsImg.MEDALS_LATEST_MEDALS_BG,
          package: Config.package,
          height: 370.rdp,
        ),
        Positioned(
          bottom: 227.rdp,
          left: 0,
          right: 0,
          child: Text(
            "最新获得",
            textAlign: TextAlign.center,
            style: context.textstyles.bodyText.pf.copyWith(
              color: context.appColors.jColorYellow5,
            ),
          ),
        ),
        Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.end,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SizedBox(
                height: 229.rdp,
                child: _buildMedalItem(),
              ),
              SizedBox(height: 8.rdp),
              if (widget.latestMedals.length > 1)
                CarouselIndicator(
                  count: widget.latestMedals.length,
                  itemSize: 6.rdp,
                  spacing: 8.rdp,
                  controller: _indicatorController,
                ),
              SizedBox(height: 11.rdp),
            ],
          ),
        ),
      ]),
    );
  }

  Widget _buildMedalItem() {
    return PageView.builder(
      controller: _pageController,
      itemCount: widget.latestMedals.length,
      itemBuilder: (context, index) {
        MedalModel medal = widget.latestMedals[index];
        DateTime dateTime = DateTime.fromMillisecondsSinceEpoch(
          medal.getTime ?? 0,
        );
        String date = DateFormat("yyyy年MM月dd日获得").format(dateTime);

        return GestureDetector(
          onTap: () {
            widget.onItemTap(medal);
          },
          child: Column(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              SizedBox(height: 32.rdp),
              _buildMedalCard(medal),
              SizedBox(height: 8.rdp),
              Text(
                date,
                style: context.textstyles.remark.pf.copyWith(
                  color: context.appColors.jColorGray4,
                ),
              ),
              SizedBox(height: 4.rdp),
              Text(
                medal.title ?? "",
                style: context.textstyles.bodyTextEmphasis.pf,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildMedalCard(MedalModel medal) {
    return BlocProvider(
      create: (_) {
        AchievementCard card = AchievementCard(0, "", medal, null);
        var ctrl = CardController(card, _state.commonResZip);
        // 始终不显示进度条和 new
        ctrl.shouldUpdateState = (CardState state) {
          return state.copyWith(
            isView: true,
            needShowProgress: false,
          );
        };
        return ctrl;
      },
      child: BlocBuilder<CardController, CardState>(
        builder: (context, state) {
          return AchievementCardWrapper(width: 140.rdp, height: 140.rdp);
        },
      ),
    );
  }
}
