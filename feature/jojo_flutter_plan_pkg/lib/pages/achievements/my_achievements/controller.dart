import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/download/jojo_download.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/my_achievements/viewmodel/card_controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/my_achievements/viewmodel/view_model.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_msg/controller.dart';

import '../../../model/aigc_medias.dart';
import '../../../service/achievements_api.dart';
import '../../../service/aigc_medias_api.dart';
import '../../../utils/file_util.dart';
import '../model/achievements_model.dart';
import '../model/medal_model.dart';
import '../util/achievement_helper.dart';
import 'state.dart';

class MyAchievementsController extends Cubit<MyAchievementsState> {
  static const String tag = "MyAchievementsController";
  final int subjectType;
  final int? partnerId;
  final AchievementsApi achievementsApi;

  final JoJoResourceManager _resourceManager = JoJoResourceManager();

  /// 首次引导中
  bool isGuiding = false;
  late ValueNotifier<bool> pagePauseNotifier = ValueNotifier(false);

  MyAchievementsController(
      {required this.subjectType,
      required this.achievementsApi,
      this.partnerId})
      : super(MyAchievementsState(PageStatus.loading));

  MyAchievementsController.withDefault(
      {required this.subjectType, this.partnerId})
      : achievementsApi = proAchievementsApi,
        // : achievementsApi = AchievementsApiMock(),
        super(MyAchievementsState(PageStatus.loading)) {
    refreshData();
  }

  @override
  close() async {
    super.close();
    _resourceManager.cancelDownload();
    CardController.resourceManager.cancelDownload();
  }

  setPageStatus(PageStatus status) {
    updateState(state.copyWith(pageStatus: status));
  }

  Future<void> refreshData() async {
    l.i(tag, "refreshData");

    AchievementsModel model =
        await achievementsApi.getSubjectMedals(subjectType, partnerId);
    if (kDebugMode) {
      final medals = model.segmentMedals?.firstOrNull?.category?.firstOrNull?.medals;
      model = model.copyWith(latestMedals: medals);
    }

    // 如果没有奖章数据，则显示错误页
    if (model.segmentMedals == null) {
      l.e(tag, "没有奖章数据");
      updateState(MyAchievementsState(PageStatus.error,
          exception: Exception("没有奖章数据")));
      return;
    }

    LatestMedals? latestMedals = _buildLatestMedals(model);
    // 尝试拉取语音
    List<String>? mediaPaths = await tryGetAigcMedia(latestMedals);
    // 下载最近获得奖章资源后才显示列表
    await _downloadZipRes(
        latestMedals?.latestMedals ?? [], model.commonRes ?? "",
        successListener: (urlMap) {
      if (File(urlMap[model.commonRes] ?? "").existsSync()) {
        // 显示列表
        MyAchievementsState state = buildState(model);
        updateState(state.copyWith(
            commonResZip: urlMap[model.commonRes], mediaPaths: mediaPaths));
      } else {
        l.e(tag, "缺少公共资源");
        updateState(MyAchievementsState(PageStatus.error,
            exception: Exception("缺少公共资源")));
      }
    }, failListener: (error) {
      l.e(tag, "下载资源失败：$error");
      updateState(
          MyAchievementsState(PageStatus.error, exception: Exception(error)));
    });
  }

  /// 获取 AIGC 语音
  Future<List<String>?> tryGetAigcMedia(LatestMedals? latestMedals) async {
    try {
      bool hasPlayed = await AchievementHelper.hasPlayedVoiceToday(subjectType);
      if (hasPlayed) {
        l.i(tag, "已经获取过今日的语音");
        return null;
      }

      // 统计最新奖章中可以播放的数量
      final count =
          latestMedals?.latestMedals.where((e) => e.isView == 1).length ?? 0;
      String sceneKey;
      if (count > 0 && count <= 3) {
        sceneKey = "MEDAL_FINISH_DETAIL_3";
      } else if (count >= 4) {
        sceneKey = "MEDAL_FINISH_DETAIL_4";
      } else {
        sceneKey = "MEDAL_FINISH_DETAIL_0";
      }

      AigcMedias aigcMedias = await aigcMediasApi.getSubjectMedals([
        {
          "sceneKey": sceneKey,
          "sceneParams": {"medalQuantity": count}
        }
      ]);

      final List<String> urls = [];
      for (final sceneResult in (aigcMedias.sceneResults ?? [])) {
        for (final media in (sceneResult.medias ?? [])) {
          if ((media.url ?? "").isNotEmpty) {
            urls.add(media.url!);
          }
        }
      }
      if (urls.isEmpty) {
        l.w(tag, "AIGC媒体URL列表为空，返回空结果");
        return [];
      }

      final Completer<List<String>> completer = Completer<List<String>>();
      final List<String> paths = List.generate(urls.length, (index) => "");

      await _resourceManager.downloadUrl(
        urls,
        isNeedCancel: false,
        successListener: (urlMap) {
          for (int i = 0; i < urls.length; i++) {
            final url = urls[i];
            final path = urlMap[url] ?? "";
            paths[i] = path;
          }
          if (!completer.isCompleted) completer.complete(paths);
        },
        failListener: (error) {
          l.e(tag, "下载语音失败: $error");
          if (!completer.isCompleted) completer.complete([]);
        },
      );

      if (paths.isNotEmpty) AchievementHelper.savePlayedVoiceToday(subjectType);
      return completer.future;
    } catch (e) {
      l.e(tag, "获取 AIGC 语音异常: $e");
      return [];
    }
  }

  MyAchievementsState buildState(
    AchievementsModel model,
  ) {
    // 构建最近获得奖章
    LatestMedals? latestMedals = _buildLatestMedals(model);
    bool onlyTrainingCamp = model.onlyTrainingCamp == 1;
    // 构建奖章列表，包含分组标题
    List<MyAchievementsItem> cardList =
        _buildMedalsList(model.segmentMedals ?? []);

    // 统计可展示的奖章数量
    int medalsCount = cardList
        .whereType<AchievementsRow>()
        .map((row) => row.cards.length)
        .fold(0, (sum, length) => sum + length);

    List<MyAchievementsItem> items = [
      if (latestMedals != null && !onlyTrainingCamp) latestMedals,
      if (cardList.isNotEmpty) ...cardList,
      if (cardList.isEmpty) EmptyTip(),
      if (model.onlyTrainingCamp == 1 && model.positionImg != null)
        FillImage(model.positionImg ?? ""),
    ];

    return MyAchievementsState(
      PageStatus.success,
      onlyTrainingCamp: onlyTrainingCamp,
      medalsCount: medalsCount,
      recentObtainedCount: latestMedals?.latestMedals.length ?? 0,
      items: _insertLineSpaces(items),
      title: model.title ?? '',
      subjectColor: model.subjectColor,
    );
  }

  LatestMedals? _buildLatestMedals(AchievementsModel model) {
    List<MedalModel> latestMedals = [];
    model.latestMedals?.forEach((element) {
      MedalModel? latestMedal = _findLatestMedal(element.groupMedals ?? []);
      if (latestMedal != null) {
        latestMedals.add(latestMedal);
      }
    });

    if (latestMedals.isNotEmpty) {
      return LatestMedals(latestMedals);
    }
    return null;
  }

  List<MyAchievementsItem> _buildMedalsList(List<SegmentMedal> segmentMedals) {
    List<MyAchievementsItem> items = [];
    for (var segment in segmentMedals) {
      // 阶段标题
      // 只有一个学段时，不展示分割小标题
      if (segment.segmentTitle?.isNotEmpty == true &&
          segmentMedals.length > 1) {
        items.add(SegmentTitle(segment.segmentTitle ?? ""));
      }

      for (MedalCategory category in segment.category ?? []) {
        // 分类标题
        if (category.name?.isNotEmpty == true) {
          items.add(CategoryTitle(category.name ?? ""));
        }

        // 先将所有 medal 分组转换为 AchievementCard 列表
        final achievementCards =
            (category.medals ?? []).map<AchievementCard>((group) {
          return AchievementCard(
            group.upgrade ?? 0,
            group.tips ?? "",
            _findLatestMedal(group.groupMedals ?? []),
            _findNextStageMedal(group.groupMedals ?? []),
          );
        }).toList();

        // 将 AchievementCard 列表分组为 AchievementsRow 列表，每 3 个一行
        // 之所以需要一行一行显示，是为了懒加载优化性能以及避免下载不可见区域的资源
        final rowCount = (achievementCards.length + 2) ~/ 3;
        final List<AchievementsRow> rows = List.generate(rowCount, (index) {
          final start = index * 3;
          final end = ((index + 1) * 3 > achievementCards.length)
              ? achievementCards.length
              : (index + 1) * 3;
          return AchievementsRow(achievementCards.sublist(start, end));
        });

        items.addAll(rows);
      }
    }

    return items;
  }

  _downloadZipRes(List<MedalModel> medals, String commonResUrl,
      {Function(Map<String, String>)? successListener,
      Function(String)? failListener}) async {
    // 奖章的资源
    List<String> urlList = medals
        .map((medal) {
          return medal.resource?["flutterRes"] ?? "";
        })
        .where((url) => url.isNotEmpty)
        .toList();
    // 公共资源
    if (commonResUrl.isNotEmpty) {
      urlList.add(commonResUrl);
    }

    await _resourceManager.downloadUrl(urlList, isNeedCancel: false,
        successListener: (urlMap) async {
      for (String url in urlList) {
        String localPath = urlMap[url] ?? "";
        if (localPath.isNotEmpty) {
          String dirPath = await unzip(localPath);
          // 删除多余文件
          await removeUselessFilesAndDirs(Directory(dirPath));

          if (Directory(dirPath).existsSync()) {
            l.i(tag, "_downloadZipRes success url: $url localPath: $localPath");
          } else {
            l.e(tag, "_downloadZipRes fail url: $url localPath: $localPath");
          }
        } else {
          l.e(tag, "_downloadZipRes fail url: $url");
        }
      }
      successListener?.call(urlMap);
    }, failListener: (error) {
      l.e(tag, "_downloadZipRes fail error: $error");
      failListener?.call(error.message??"");
    });
  }

  /// 在列表中插入行间隔
  List<MyAchievementsItem> _insertLineSpaces(List<MyAchievementsItem> items) {
    List<MyAchievementsItem> newItems = [];
    for (int i = 0; i < items.length; i++) {
      MyAchievementsItem item = items[i];
      MyAchievementsItem? nextItem = i + 1 < items.length ? items[i + 1] : null;

      if (i == 0 && item is EmptyTip) {
        newItems.add(item);
        newItems.add(LineSpace(28.rdp));
      } else if (i == 0 && item is! LatestMedals) {
        newItems.add(LineSpace(20.rdp));
        newItems.add(item);
        newItems.add(LineSpace(28.rdp));
      } else if (item is CategoryTitle && nextItem is AchievementsRow) {
        newItems.add(item);
        newItems.add(LineSpace(8.rdp));
      } else {
        newItems.add(item);
        newItems.add(LineSpace(28.rdp));
      }
    }
    return newItems;
  }

  /// 找到最近获得的勋章
  MedalModel? _findLatestMedal(List<MedalModel> medals) {
    return medals.where((medal) => medal.isGet == 1).fold<MedalModel?>(null,
        (maxMedal, medal) {
      return (maxMedal == null ||
              (medal.getTime ?? 0) > (maxMedal.getTime ?? 0))
          ? medal
          : maxMedal;
    });
  }

  /// 找到下一阶段的勋章
  MedalModel? _findNextStageMedal(List<MedalModel> medals) {
    return medals.where((medal) => medal.isGet == 0).fold<MedalModel?>(null,
        (nextStageMedal, medal) {
      return (nextStageMedal == null ||
              (medal.progress?.total ?? 0) <
                  (nextStageMedal.progress?.total ?? 1))
          ? medal
          : nextStageMedal;
    });
  }

  Future<void> updateState(MyAchievementsState state) async {
    super.emit(state);
  }

  bool isVisitorAttitude() {
    return partnerId != null;
  }

  onPause() {
    pagePauseNotifier.value = true;
  }

  onResume() {
    pagePauseNotifier.value = false;
  }
}
