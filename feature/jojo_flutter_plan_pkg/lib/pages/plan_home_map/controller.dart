import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/config/config.dart';
import 'package:jojo_flutter_base/download/jojo_download.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import 'package:jojo_flutter_base/widgets/dialog/base_dialog_widget.dart';
import 'package:jojo_flutter_base/widgets/dialog/dialog_ext.dart';
import 'package:jojo_flutter_plan_pkg/ext/list_ext.dart';
import 'package:jojo_flutter_plan_pkg/ext/string_ext.dart';
import 'package:jojo_flutter_plan_pkg/page.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_auto_transform/ext.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/utils/dialog_course_helper.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/state.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/widget/dialogs/home_personal_image_guide_dialog.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_msg/controller.dart';
import 'package:jojo_flutter_plan_pkg/service/home_map_page_api.dart';
import 'package:jojo_flutter_plan_pkg/utils/file_util.dart';

import '../../common/host_env/host_env.dart';
import '../../service/guide_api.dart';
import '../../utils/personal_snapshop.dart';
import '../plan_home/calender/model/calender_vent.dart';
import '../plan_home/utils/ext.dart';
import 'eventbus/event_bus_location_classkey_data.dart';
import 'model/course_advertisements_data.dart';
import 'model/course_map_home_user_info_data.dart';
import 'model/course_subject_clean_data.dart';
import 'widget/dialogs/test_dialog.dart';

class PlanHomeWithMapviewCtrl extends Cubit<PlanHomeMapViewState> {
  HomeMapPageApi? homePageApi;
  HomeMapPageApi? homePageApiMock;

  CourseSubjectCleanData? cleanCourseData;
  Map<String, String>? tabAnimationFilePathMap; // 每个科目对应的动效本地路径

  bool isShowIncentiveDialog = false;
  PersonalImageRectEvent? personalImageRect;
  StreamSubscription? _personalImageRectEventBus;

  //课表定位逻辑
  StreamSubscription? _locationEventBus;
  //监听小窗状态
  StreamSubscription? _miniWindowStatusEventBus;

  Map<int,TopUserInfo> topUserInfoMap = {};
  bool didCacheTopInfo = false;

  final JoJoResourceManager _resourceManager = JoJoResourceManager();

  

  PlanHomeWithMapviewCtrl(
      {HomeMapPageApi? api, String? routerRedirect, String? classKey})
      : super(PlanHomeMapViewState(
            pageStatus: PageStatus.loading,
            featureSection: FeatureSection.courseList)) {
    homePageApi = api ?? homeMapPageApiService;
    homePageApiMock = api ?? homeMapPageApiServiceMock;

    //监听路由定位
    _listenLocationCourse();
    //监听小窗状态
    _listenMiniWindowStatus();
    //监听个人头像位置
    _listenPersonalImageRect();
    l.i(tag, "routerRedirect==$routerRedirect,classKey==$classKey");
    if (routerRedirect == "1" && classKey.isNotNullOrEmpty()) {
      state.routeClassKey = classKey;
    }
  }
  //监听小窗状态
  void _listenMiniWindowStatus() {
    _miniWindowStatusEventBus =
        jojoEventBus.on<NativeNotificationMinWindowShowEvent>().listen((event) {
      if (!event.minWindowOpen) {
        onRefresh();
      }
    });
  }

  //监听路由定位,定位课程
  void _listenLocationCourse() {
    _locationEventBus =
        jojoEventBus.on<EventBusLocationClassKeyData>().listen((event) async {
      l.i(tag, "收到路由定位: ${event.classKey}");
      final locationSubjectData =
          cleanCourseData?.subjectTabs?.firstWhereOrNull((element) {
        final findClassIndex = element.userClassVoList
                ?.indexWhere((it) => it.classKey == event.classKey) ??
            -1;
        return findClassIndex > -1;
      });
      if (locationSubjectData != null) {
        positionToCourse(locationSubjectData.subjectType, event.classKey);
      } else {
        //跳转课表页
        RunEnv.jumpLink(
            "tinman-router://cn.tinman.jojoread/flutter/plan/lessonList?windowType=normal&deviceOrientation=auto&classKey=${event.classKey}");
      }
    });
  }

  @visibleForTesting
  void testableListenPersonalImageRect() => _listenPersonalImageRect();
  void _listenPersonalImageRect() {
    _personalImageRectEventBus =
        jojoEventBus.on<PersonalImageRectEvent>().listen((event) {
      personalImageRect = event;
    });
  }

  refreshPage() {
    safeEmit(state.copyWith());
  }

  onResume() {
    onRefresh();
    state.addResumeCount();
  }

  _cacheOpenTabData(int curType) async{
    
    if(didCacheTopInfo){
      return;
    }
    didCacheTopInfo = true;
    final length = cleanCourseData?.subjectTabs?.length ?? 0;
    for(int i = 0;i < length;++i){
      final element = cleanCourseData?.subjectTabs?[i];
      final subjectType = element?.subjectType ?? -1;
      if (subjectType != curType) {
          TopUserInfo? topUserInfo = await homePageApi?.requestTopUserInfo(subjectType);
          if(topUserInfo != null){
            topUserInfoMap[subjectType] = topUserInfo;
            final resource = topUserInfo.continuous?.resource ?? '';
            final clientDynamic = topUserInfo.continuous?.clientDynamic ?? '';
            _downloadResourceAndRefresh(subjectType,resource,clientDynamic,false);
            l.i('plan_home_tab', '缓存科目数据:$subjectType');
          }
      }
    }  
  }

  /// 刷新数据接口
  /// @param isSlient 是否静默刷新，静默刷新不更新状态
  onRefresh() async {
    bool isSlient = (state.tabData != null);
    final timeStart = DateTime.now().millisecondsSinceEpoch;
    l.d('testIn', " map onRefresh $timeStart)");
    try {
      if (isSlient == false) {
        final newState = state.copyWith()..pageStatus = PageStatus.loading;
        safeEmit(newState);
      }
      //科目和课程数据
      final courseResponse = await homePageApi?.getUserSubjectClass();
      //扩课数据
      final recommendResponse = await getRecommendData();
      //清洗数据
      cleanCourseData = await CourseSubjectCleanData.cleanDataFromOriginData(
          courseResponse, recommendResponse);
      if (cleanCourseData?.isNoCourse() == true) {
        //无课用户
        state.featureSection = FeatureSection.noCourse;

        //请求纯新用户数据（无课用户）
        final pureNewUserPageShowData =
            await homePageApi?.getPureNewUserPageShowData();
        state.mainPictureUrl = pureNewUserPageShowData?.mainPictureUrl;
        state.mainButtonDesc = pureNewUserPageShowData?.mainButtonDesc;
        state.mainButtonLinkUrl = pureNewUserPageShowData?.mainButtonLinkUrl;
        state.subButtonDesc = pureNewUserPageShowData?.subButtonDesc;
        state.subButtonLinkUrl = pureNewUserPageShowData?.subButtonLinkUrl;
      } else {
        //有课用户
        state.featureSection = FeatureSection.courseList;
        if (isSlient == false) {
          //冷启动的时候设置初始科目
          state.currentSubjectData = cleanCourseData?.subjectTabs
              ?.firstWhereOrNull((element) =>
                  element.subjectType == cleanCourseData?.isSelectSubjectType);

          //如果路由需要定位,优先处理定位
          if (state.routeClassKey.isNotNullOrEmpty()) {
            final locationSubjectData =
                cleanCourseData?.subjectTabs?.firstWhereOrNull((element) {
              final findClassIndex = element.userClassVoList?.indexWhere(
                      (it) => it.classKey == state.routeClassKey) ??
                  -1;
              return findClassIndex > -1;
            });
            if (locationSubjectData != null) {
              state.currentSubjectData = locationSubjectData;
            } else {
              //跳转课表页
              RunEnv.jumpLink(
                  "tinman-router://cn.tinman.jojoread/flutter/plan/lessonList?windowType=normal&deviceOrientation=auto&classKey=${state.routeClassKey}");
            }
            state.locationClassKey = state.routeClassKey;
            state.routeClassKey = null;
          }
        } else {
          //处理气泡数据
          final locationSubjectData = cleanCourseData?.subjectTabs
              ?.firstWhereOrNull((element) =>
                  element.subjectType == state.currentSubjectData?.subjectType);
          if (locationSubjectData != null) {
            state.currentSubjectData = locationSubjectData;
          }
        }

        //请求顶部tab信息
        try {
          final reqType = state.currentSubjectData?.subjectType ??
                  cleanCourseData?.isSelectSubjectType ??
                  -1;
          l.i('csl====', 'requestTopUserInfo onRefresh:$reqType');
          TopUserInfo? topUserInfo = await homePageApi?.requestTopUserInfo(reqType);
          if (kDebugMode) {
            final continuo = topUserInfo?.continuous?.copyWith(bestDays: isSlient ? 3: 2);
            topUserInfo = topUserInfo?.copyWith(continuous: continuo);
          }

          await Future.delayed(const Duration(milliseconds: 1200));
          
          _cacheOpenTabData(reqType);// 缓存其它科目的数据，不用await避免阻塞
          
          if(topUserInfo != null){
            topUserInfoMap[reqType] = topUserInfo;
            l.i('plan_home_tab', '缓存选中科目数据:$reqType');
          }
          int isFinishLesson = topUserInfo?.continuous?.isFinishLesson ?? 0;
          int isFinishLessonPopup =
              topUserInfo?.continuous?.isFinishLessonPopup ?? 0;
          state.showIncentiveModule = isFinishLesson == 1; //只有已经完课时候才显示顶部的个人激励模块
          state.showNewUserModule = isFinishLesson == 0; //纯新用户
          state.topUserInfo = topUserInfo;
          state.isCacheTopInfo = false;

          String? localPersonalImagePath = await checkRecentImage();
          state.localPersonalImagePath = localPersonalImagePath;
        } catch (e) {
          l.e(tag, "请求顶部激励信息失败$e");
        }

        //解析所有课程选中情况
        if (state.currentSubjectIndex == null) {
          state.currentSubjectIndex = {};
          cleanCourseData?.subjectTabs?.forEach((element) {
            if (element.userClassVoList.isNullOrEmpty() == true) {
              //超出缓冲期课
              state.currentSubjectIndex?[element.subjectType ?? -1] = -1;
            } else {
              state.currentSubjectIndex?[element.subjectType ?? -1] = 0;
            }
          });
        }
      }

      final newState = state.copyWith()
        ..pageStatus = PageStatus.success
        ..tabData = cleanCourseData
        ..topUserRequestSubjectType = state.currentSubjectData?.subjectType;
      safeEmit(newState);
      setAppWidgetShowData(cleanCourseData);
      //气泡消失逻辑
      clickTabViewBubble(state.currentSubjectData?.bubbleList, true,
          state.currentSubjectData?.subjectType ?? -1);
      clickTabViewBubble(getCurrentCourse()?.bubbleList, false,
          getCurrentCourse()?.classId ?? -1);

      // 下载连续学动效资源
      final subjectType = state.currentSubjectData?.subjectType ?? 0;
      final clientDynamic = state.topUserInfo?.continuous?.clientDynamic ?? '';
      final resource = state.topUserInfo?.continuous?.resource ?? '';
      _downloadResourceAndRefresh(subjectType,resource,clientDynamic,true);
    } catch (e) {
      setAppWidgetShowData(null);
      //请求失败
      l.e(tag, "数据接口请求或者处理数据失败$e");
      if (isSlient == false) {
        var exception = Exception("$e");
        if (e is Exception) {
          exception = e;
        }
        final newState = state.copyWith()
          ..pageStatus = PageStatus.error
          ..exception = exception;
        safeEmit(newState);
      }
    } finally {
      DialogCourseHelper().setController(
          this,
          state.currentSubjectData?.subjectType ??
              cleanCourseData?.isSelectSubjectType ??
              -1);
      DialogCourseHelper()
          .setCourseTab(state, positionToCourse, refreshTopIncentiveData);

          final time2 = DateTime.now().millisecondsSinceEpoch;
          final total = (time2 - timeStart);
      l.d('testIn', " map onRefresh end $time2, total:$total)");
      if (getCurrentClassKey().isEmpty) {
        DialogCourseHelper().setCourseLessonState(null);
      }
    }
  }

  @override
  Future<void> close() {
    DialogCourseHelper().setController(null, -1);
    return super.close();
  }

  void _downloadResourceAndRefresh(int subjectType,String resource,String clientDynamic,bool refresh) async {
    l.i('plan_home_tab_csl', 'download subjectType:$subjectType refresh:$refresh resource:$resource');
    // 下载动效资源包
    tabAnimationFilePathMap ??= {};
    
    String url = tabAnimationFilePathMap!["$subjectType"] ?? '';
    final didDownload = url.isNotEmpty && url == resource; // 下载过并且下载地址相同
    if(didDownload){
      tabAnimationFilePathMap!["${subjectType}_clientDynamic"] = clientDynamic;// 更新当前clientDynamic
      if(refresh && resource == state.topUserInfo?.continuous?.resource && subjectType == state.currentSubjectData?.subjectType){
        final newState = state.copyWith();
        safeEmit(newState);
        l.i('plan_home_tab_csl', 'download cache 刷新:$subjectType refresh:$refresh resource:$resource');
      }else{
        l.i('plan_home_tab_csl', 'download cache success 不刷新:$subjectType refresh:$refresh resource:$resource');
      }
      l.i('plan_home_tab_csl', 'download cache 直接返回:$subjectType refresh:$refresh resource:$resource');
      return;
    }

    int timestampInMillis = DateTime.now().millisecondsSinceEpoch;
    _downloadZipRes(resource,successListener: (map) {        
      l.i('plan_home_tab', '_downloadResourceAndRefresh:${DateTime.now().millisecondsSinceEpoch - timestampInMillis} url:$resource');
      String url = map["url"] ?? "";
      String path = map["path"] ?? "";
      // 与当前是同一个资源才处理刷新，避免覆盖其他科目的小鸡动图
      String subjectTypeUrl = map["$subjectType"] ?? "";
      if (subjectTypeUrl.isEmpty) {
        tabAnimationFilePathMap!["$subjectType"] = url;
        tabAnimationFilePathMap!["${subjectType}_path"] = path;
        tabAnimationFilePathMap!["${subjectType}_clientDynamic"] = clientDynamic;
      }
      if(refresh && resource == state.topUserInfo?.continuous?.resource && subjectType == state.currentSubjectData?.subjectType){
        l.i('plan_home_tab_csl', 'download success 刷新:$subjectType refresh:$refresh resource:$resource');
        final newState = state.copyWith();
        safeEmit(newState);
      }else{
        l.i('plan_home_tab_csl', 'download success 不刷新:$subjectType refresh:$refresh resource:$resource');
      }
    });
  }

  /// 获取扩课数据
  Future<CourseAdvertisementsData?> getRecommendData() async {
    try {
      final recommendResponse =
          await homePageApi?.requestAdvertisements(scene: "moreSubjects");
      return recommendResponse;
    } catch (e) {
      //请求失败
      l.e(tag, "扩课接口请求失败$e");
      return null;
    }
  }

  /// 刷新顶部激励数据
  refreshTopIncentiveData() async {

    int timestampInMillis = DateTime.now().millisecondsSinceEpoch;
    int reqType = state.currentSubjectData?.subjectType ?? 0;
    TopUserInfo? cacheTopInfo;
    if(topUserInfoMap.containsKey(reqType)){
      l.i('plan_home_tab', '使用缓存数据先刷新');
      final topUserInfo = topUserInfoMap[reqType];
      cacheTopInfo = topUserInfo;
      int isFinishLesson = topUserInfo?.continuous?.isFinishLesson ?? 0;
      
      final newState = state.copyWith()
      ..topUserInfo = topUserInfo
      ..topAnimatorDuring = 0 //接口刷新不需要动画切换
      ..showNewUserModule = isFinishLesson == 0 //纯新用户
      ..showIncentiveModule = isFinishLesson == 1 //只有已经完课且没有捏脸弹窗时候才显示顶部的个人激励模块
      ..localPersonalImagePath = state.localPersonalImagePath
      ..isCacheTopInfo = true
      ..topUserRequestSubjectType = reqType;
      
      safeEmit(newState);

      final resource = cacheTopInfo?.continuous?.resource ?? "";
      final clientDynamic = cacheTopInfo?.continuous?.clientDynamic ?? '';
      _downloadResourceAndRefresh(reqType,resource, clientDynamic ,true);
    }
    l.i('plan_home_tab', 'requestTopUserInfo refreshTopIncentiveData:$reqType');
    TopUserInfo? topUserInfo = await homePageApi?.requestTopUserInfo(reqType);// 重新请求的数据
    
    /* 先去掉避免重复刷新的逻辑
    if(cacheTopInfo != null && topUserInfo != null){
      final cacheDressImg = cacheTopInfo.dress?.img ?? "";
      final dressImg = topUserInfo.dress?.img ?? "";

      Uri cacheUrl = Uri.parse(cacheDressImg);
      
      // 重建基础 URL（不含查询参数）
      String cacheUrlPath = "${cacheUrl.scheme}://${cacheUrl.host}${cacheUrl.path}";

      Uri url = Uri.parse(dressImg);
      // 重建基础 URL（不含查询参数）
      String urlPath = "${url.scheme}://${url.host}${url.path}";
      if(cacheUrlPath == urlPath){
        // cacheTopInfo.dress?.img = dressImg;
      }
    }

    if(topUserInfo.toString() == cacheTopInfo.toString()){
      l.i('plan_home_tab', '请求的数据和缓存一样不再刷新');
      return;
    }
    */
    
    if(topUserInfo != null){// 存储最新的数据
      topUserInfoMap[reqType] = topUserInfo;
    }
    
    if(reqType != state.currentSubjectData?.subjectType){
      l.i('plan_home_tab', 'refreshTopIncentiveData 脏数据，直接返回reqType:$reqType curType:${state.currentSubjectData?.subjectType}');
      return;// 避免脏数据，当前的tab和请求回来的tab不一致，直接返回
    }
    
    l.i('plan_home_tab', '请求的数据和缓存数据不一样，刷新页面');
    String? localPersonalImagePath = await checkRecentImage();
    
    l.i('refreshTopIncentiveData', 'time:${DateTime.now().millisecondsSinceEpoch - timestampInMillis }');
    int isFinishLesson = topUserInfo?.continuous?.isFinishLesson ?? 0;

    final newState = state.copyWith()
      ..topUserInfo = topUserInfo
      ..topAnimatorDuring = 0 //接口刷新不需要动画切换
      ..showNewUserModule = isFinishLesson == 0 //纯新用户
      ..showIncentiveModule = isFinishLesson == 1 //只有已经完课且没有捏脸弹窗时候才显示顶部的个人激励模块
      ..localPersonalImagePath = localPersonalImagePath
      ..isCacheTopInfo = false
      ..topUserRequestSubjectType = reqType;

    safeEmit(newState);

    // 查看动效资源是否下载过，如果没有，则下载对应资源
    final resource = topUserInfo?.continuous?.resource ?? "";
    final clientDynamic = topUserInfo?.continuous?.clientDynamic ?? '';
    _downloadResourceAndRefresh(reqType,resource, clientDynamic,true);
  }

  refreshTopActivityPicBackground(String? activityPic, String? activityPadPic,
      String? activityPersonalImage) {
    final newState = state.copyWith()
      ..activityPicHeader = activityPic
      ..activityPicHeaderPad = activityPadPic
      ..activityPersonalImage = activityPersonalImage;
    safeEmit(newState);
  }

  refreshTopIncentiveDataByCount(int rewardType, int count, int during) {
    TopUserInfo? topUserInfo = state.copyWith().topUserInfo;
    if (topUserInfo != null &&
        topUserInfo.learnBean != null &&
        topUserInfo.learnBean?.type == rewardType) {
      topUserInfo.learnBean?.amount =
          (topUserInfo.learnBean?.amount ?? 0) + count;
    }
    if (topUserInfo != null &&
        topUserInfo.medal != null &&
        topUserInfo.medal?.type == rewardType) {
      topUserInfo.medal?.amount = (topUserInfo.medal?.amount ?? 0) + count;
    }
    final newState = state.copyWith()
      ..topAnimatorDuring = during
      ..topUserInfo = topUserInfo;
    safeEmit(newState);
  }

  // 设置每个科目选中的课程(这里存储index而不是课程信息,因为会存在静默刷新,coursekey变化)
  setCurrentSubjectCourse(int? subjectType, int courseIndex) {
    state.currentSubjectIndex?[subjectType ?? -1] = courseIndex;
  }

  //获取当前显示的课程
  String getCurrentClassKey() {
    final currentSubjectType = state.currentSubjectData?.subjectType ?? -1;
    final currentSubjectCourseIndex =
        state.currentSubjectIndex?[currentSubjectType] ?? -1;
    if (currentSubjectCourseIndex > -1) {
      // 数据正常
      final currentSubject = cleanCourseData?.subjectTabs?.firstWhereOrNull(
          (element) => element.subjectType == currentSubjectType);
      if (currentSubject == null ||
          currentSubject.isNoServiceCourse() == true) {
        //无服务期
        return "";
      }
      if ((currentSubject.userClassVoList?.length ?? 0) >
          currentSubjectCourseIndex) {
        final currentCourse =
            currentSubject.userClassVoList?[currentSubjectCourseIndex];
        if (currentCourse == null || currentCourse is BufferClassData) {
          //缓冲期课
          return "";
        } else {
          // 服务期课
          return currentCourse.classKey ?? "";
        }
      }
    }

    // 数据异常
    return "";
  }

  //获取当前显示的课程
  BaseUserClassListCleanData? getCurrentCourse() {
    final currentSubjectType = state.currentSubjectData?.subjectType ?? -1;
    final currentSubjectCourseIndex =
        state.currentSubjectIndex?[currentSubjectType] ?? -1;
    if (currentSubjectCourseIndex > -1) {
      // 数据正常
      final currentSubject = cleanCourseData?.subjectTabs?.firstWhereOrNull(
          (element) => element.subjectType == currentSubjectType);
      if (currentSubject == null ||
          currentSubject.isNoServiceCourse() == true) {
        //无服务期
        return null;
      }
      if ((currentSubject.userClassVoList?.length ?? 0) >
          currentSubjectCourseIndex) {
        final currentCourse =
            currentSubject.userClassVoList?[currentSubjectCourseIndex];
        return currentCourse;
      }
    }

    // 数据异常
    return null;
  }

  //点击了推荐科目
  clickRecommendCourse(int subjectType) async {
    final localRecommendSubject = await jojoNativeBridge
        .operationNativeValueGet(key: keyRecommendSubjectClick);
    var result = (localRecommendSubject.data?.value ?? "").split(",");
    if (!result.contains(subjectType.toString())) {
      result.add(subjectType.toString());
    }
    jojoNativeBridge.operationNativeValueSet(
        key: keyRecommendSubjectClick, value: result.join(","));

    state.tabData?.recommendSubjectInfo?.isShowBubble = false;
    safeEmit(state.copyWith());
  }

  //设置小组件弹窗需要的数据，需要用户(拥有待开始或进行中的年课或训练营)
  setAppWidgetShowData(CourseSubjectCleanData? data) {
    final isShow = data?.subjectTabs?.any((element) =>
        element.userClassVoList?.any((it) =>
            (it.classStatus == 1 || it.classStatus == 2) &&
            (it.courseType == 1 || it.courseType == 3)) ==
        true);
    jojoNativeBridge.operationNativeValueSet(
        key: "key_app_widget_show_data", value: isShow == true ? "1" : "0");
  }

  //气泡处理
  clickTabViewBubble(
      List<BubbleCleanData>? bubbleList, bool isSubject, int dataRefreshId) {
    if (bubbleList.isNullOrEmpty()) return;
    bubbleList?.forEach((bubbleData) async {
      if ((bubbleData.callBackType ?? -1) > 0) {
        //需要接口反馈
        _reportGuideShow(
            bubbleData.callBackType ?? -1, bubbleData.dataList ?? []);
      } else {
        //需要记录本地
        final localGuideShow = await jojoNativeBridge.operationNativeValueGet(
            key: isSubject
                ? keySubjectBubbleLocalDataKey
                : keyCourseBubbleLocalDataKey);
        final localGuideShowData = localGuideShow.data?.value ?? "";
        final localGuideShowList = localGuideShowData.split(",");
        var saveValue =
            "${BaseConfig.share.userInfo?.uid}-${bubbleData.dataList?.firstOrNull ?? ""}";
        if (bubbleData.isNeedDate == true) {
          String formattedDate =
              DateFormat('yyyy-MM-dd').format(DateTime.now());
          saveValue += "-$formattedDate";
        }
        if (!localGuideShowList.contains(saveValue)) {
          localGuideShowList.add(saveValue);
          jojoNativeBridge.operationNativeValueSet(
              key: isSubject
                  ? keySubjectBubbleLocalDataKey
                  : keyCourseBubbleLocalDataKey,
              value: localGuideShowList.join(","));
        }
      }
    });

    //刷新界面
    if (isSubject) {
      //科目气泡
      state.tabData?.subjectTabs
          ?.firstWhereOrNull((element) => element.subjectType == dataRefreshId)
          ?.bubbleList = null;
    } else {
      //课程气泡
      state.tabData?.subjectTabs?.forEach((subjectInfo) {
        subjectInfo.userClassVoList?.forEach((course) {
          if (course.classId == dataRefreshId) {
            course.bubbleList = null;
          }
        });
      });
    }

    safeEmit(state.copyWith());
  }

  // 定位科目或者课程（没有的字段传null）
  positionToCourse(int? subjectType, String? courseKey) {
    if (subjectType != null) {
      state.locationSubjectType = subjectType;
      safeEmit(state.copyWith());
    }
    if (courseKey.isNotNullOrEmpty()) {
      print("开始定位$courseKey");
      state.locationClassKey = courseKey;
      safeEmit(state.copyWith());
    }
  }

  // 上报气泡显示
  _reportGuideShow(int type, List<int?> dataIdList) async {
    try {
      for (var dataId in dataIdList) {
        final Map<String, dynamic> map = {
          "bizIdList": ["$type-$dataId"],
          "type": type,
          "userId": BaseConfig.share.userInfo?.uid ?? "",
          "classId": dataId,
        };
        GuideAPIService.reportGuideShow(map);
      }
    } catch (e) {
      l.e("25上课页", "_reportGuideShow error message: $e");
    }
  }

  showAddTeacherDialog(int popupType, String reason) async {
    bool isShow =
        await DialogCourseHelper().showAddTeacherDialog(popupType, reason);
    return isShow;
  }

  Future<void> addTestDialogs(
      List<BaseDialogWidget> _dialogSequenceList) async {
    const key = "_TestDialogBState_type_";
    const testDialogKeyOne = "testDialogKeyOne";
    final testKeyDataOne = await jojoNativeBridge.operationNativeValueGet(
        key: key + testDialogKeyOne);
    _dialogSequenceList.add(
      TestDialogB(
        bgColor: Colors.blueAccent,
        pagePath: AppPage.planHomeMapPage.path,
        dialogKey: testDialogKeyOne,
        dialogSort: 97,
        dialogType: testKeyDataOne.data?.value ?? subTypeMust,
      ),
    );

    const testDialogKeyTwo = "testDialogKeyTwo";
    final testKeyDataTwo = await jojoNativeBridge.operationNativeValueGet(
        key: key + testDialogKeyTwo);
    _dialogSequenceList.add(
      TestDialogB(
        bgColor: Colors.yellowAccent,
        pagePath: AppPage.planHomeMapPage.path,
        dialogKey: testDialogKeyTwo,
        dialogSort: 98,
        dialogType: testKeyDataTwo.data?.value ?? subTypeMust,
      ),
    );

    const testDialogKeyThree = "testDialogKeyThree";
    final testKeyDataThree = await jojoNativeBridge.operationNativeValueGet(
        key: key + testDialogKeyThree);
    _dialogSequenceList.add(
      TestDialogB(
        bgColor: Colors.redAccent,
        pagePath: AppPage.planHomeMapPage.path,
        dialogKey: testDialogKeyThree,
        dialogSort: 99,
        dialogType: testKeyDataThree.data?.value ?? subTypeMust,
      ),
    );
  }

  /// 下载动效资源zip并解压
  Future<void> _downloadZipRes(String resUrl,
      {Function(Map<String, String>)? successListener}) async {
    // 资源包地址
    if (resUrl.isEmpty) {
      return;
    }
    final List<String> urlList = [resUrl];
    await _resourceManager.downloadUrl(
      urlList,
      isNeedCancel: false,
      successListener: (urlMap) async {
        for (final url in urlList) {
          final String localPath = urlMap[url] ?? "";
          if (localPath.isNotEmpty) {
            try {
              final String dirPath = await unzip(localPath);
              if (Directory(dirPath).existsSync()) {
                l.i(tag,
                    "_downloadZipRes success url: $url localPath: $localPath");
                if (successListener != null) {
                  successListener({"url": resUrl, "path": dirPath});
                }else{
                  l.e(tag, "_downloadZipRes fail successListener none: $url");    
                }
              }else{
                l.e(tag, "_downloadZipRes fail dirPath none: $url");  
              }
            } catch (e) {
              l.e(tag, "_downloadZipRes fail url: $url");
            }
          } else {
            l.e(tag, "_downloadZipRes fail url: $url");
          }
        }
      },
      failListener: (error) {
        l.e(tag, "_downloadZipRes fail error: $error");
      },
    );
  }

  dispose() {
    _locationEventBus?.cancel();
    _miniWindowStatusEventBus?.cancel();
    _personalImageRectEventBus?.cancel();
  }
}

// 本地存储推荐科目点击事件
const keyRecommendSubjectClick = "key_recommend_subject_click";
const tag = "2025上课页";
const tagHome = "CoursePlanFlutter2025Fragment";
