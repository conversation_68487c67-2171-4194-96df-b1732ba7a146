import 'package:jojo_flutter_base/base.dart';

part 'personal_info_data.freezed.dart';
part 'personal_info_data.g.dart';

// 个人主页顶部个人信息数据
@freezed
class PersonalInfo with _$PersonalInfo {
  const factory PersonalInfo(
      {String? nickname, //昵称
      int? onlyTrainingCamp, //是否仅训练营(1是0否)
      int? studyDays, //学习天数
      String? dressImg, //装扮图
      @Default(1) int? like, //点赞数
      @Default('') String? likeDescription, //点赞描述
      @Default('') String? dressUpIcon, //装扮图标
      @Default(0) int? newDressUp, //是否新装扮(1是0否)
      String? newDressUpIcon, //上新图标
      @Default(0) int canLike, //是否可以点赞(1否0可)
      List<EntranceInfo>? entranceList,
      MessageInfo? messageInfo,
      @Default(-1) int? partnerStatus 
      }) = _PersonalInfo;

  factory PersonalInfo.fromJson(Map<String, dynamic> json) =>
      _$PersonalInfoFromJson(json);
}

@freezed
class MessageInfo with _$MessageInfo {
  const factory MessageInfo(
      {@Default(0) int? unreadNum,
      @Default("") String? jumpRoute,
      @Default("") String? icon}) = _MessageInfo;

  factory MessageInfo.fromJson(Map<String, dynamic> json) =>
      _$MessageInfoFromJson(json);
}

@freezed
class EntranceInfo with _$EntranceInfo {
  const factory EntranceInfo({
    @Default("") String? type,
    @Default("") String? icon,
    @Default("") String? name,
    @Default("") String? desc,
    @Default(0) int? num,
    @Default(0) int? lastNum,
    @Default("") String? jumpRoute,
    @Default("") String? jumpDesc,
    @Default(0) int? redPoint,
    @Default(0) int? callBackParam,
  }) = _EntranceInfo;

  factory EntranceInfo.fromJson(Map<String, dynamic> json) =>
      _$EntranceInfoFromJson(json);
}
