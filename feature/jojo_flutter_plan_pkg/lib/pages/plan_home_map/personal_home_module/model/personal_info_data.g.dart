// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'personal_info_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_PersonalInfo _$$_PersonalInfoFromJson(Map<String, dynamic> json) =>
    _$_PersonalInfo(
      nickname: json['nickname'] as String?,
      onlyTrainingCamp: json['onlyTrainingCamp'] as int?,
      studyDays: json['studyDays'] as int?,
      dressImg: json['dressImg'] as String?,
      like: json['like'] as int? ?? 1,
      likeDescription: json['likeDescription'] as String? ?? '',
      dressUpIcon: json['dressUpIcon'] as String? ?? '',
      newDressUp: json['newDressUp'] as int? ?? 0,
      newDressUpIcon: json['newDressUpIcon'] as String?,
      canLike: json['canLike'] as int? ?? 0,
      entranceList: (json['entranceList'] as List<dynamic>?)
          ?.map((e) => EntranceInfo.fromJson(e as Map<String, dynamic>))
          .toList(),
      messageInfo: json['messageInfo'] == null
          ? null
          : MessageInfo.fromJson(json['messageInfo'] as Map<String, dynamic>),
      partnerStatus: json['partnerStatus'] as int? ?? -1,
    );

Map<String, dynamic> _$$_PersonalInfoToJson(_$_PersonalInfo instance) =>
    <String, dynamic>{
      'nickname': instance.nickname,
      'onlyTrainingCamp': instance.onlyTrainingCamp,
      'studyDays': instance.studyDays,
      'dressImg': instance.dressImg,
      'like': instance.like,
      'likeDescription': instance.likeDescription,
      'dressUpIcon': instance.dressUpIcon,
      'newDressUp': instance.newDressUp,
      'newDressUpIcon': instance.newDressUpIcon,
      'canLike': instance.canLike,
      'entranceList': instance.entranceList,
      'messageInfo': instance.messageInfo,
      'partnerStatus': instance.partnerStatus,
    };

_$_MessageInfo _$$_MessageInfoFromJson(Map<String, dynamic> json) =>
    _$_MessageInfo(
      unreadNum: json['unreadNum'] as int? ?? 0,
      jumpRoute: json['jumpRoute'] as String? ?? "",
      icon: json['icon'] as String? ?? "",
    );

Map<String, dynamic> _$$_MessageInfoToJson(_$_MessageInfo instance) =>
    <String, dynamic>{
      'unreadNum': instance.unreadNum,
      'jumpRoute': instance.jumpRoute,
      'icon': instance.icon,
    };

_$_EntranceInfo _$$_EntranceInfoFromJson(Map<String, dynamic> json) =>
    _$_EntranceInfo(
      type: json['type'] as String? ?? "",
      icon: json['icon'] as String? ?? "",
      name: json['name'] as String? ?? "",
      desc: json['desc'] as String? ?? "",
      num: json['num'] as int? ?? 0,
      lastNum: json['lastNum'] as int? ?? 0,
      jumpRoute: json['jumpRoute'] as String? ?? "",
      jumpDesc: json['jumpDesc'] as String? ?? "",
      redPoint: json['redPoint'] as int? ?? 0,
      callBackParam: json['callBackParam'] as int? ?? 0,
    );

Map<String, dynamic> _$$_EntranceInfoToJson(_$_EntranceInfo instance) =>
    <String, dynamic>{
      'type': instance.type,
      'icon': instance.icon,
      'name': instance.name,
      'desc': instance.desc,
      'num': instance.num,
      'lastNum': instance.lastNum,
      'jumpRoute': instance.jumpRoute,
      'jumpDesc': instance.jumpDesc,
      'redPoint': instance.redPoint,
      'callBackParam': instance.callBackParam,
    };
