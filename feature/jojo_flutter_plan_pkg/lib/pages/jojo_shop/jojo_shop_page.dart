import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/app_bars/appbar_left.dart';
import 'package:jojo_flutter_base/widgets/app_bars/jojo_appbar.dart';
import 'package:jojo_flutter_base/widgets/common/adaptive_orientation_layout.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import 'package:jojo_flutter_plan_pkg/pages/jojo_shop/jojo_shop_controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/jojo_shop/model/jojo_shop_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/jojo_shop/model/jojo_shop_state.dart';
import 'package:jojo_flutter_plan_pkg/pages/jojo_shop/widget/jojo_shop_banner.dart';
import 'package:jojo_flutter_plan_pkg/pages/jojo_shop/widget/jojo_shop_head_widget.dart';
import 'package:jojo_flutter_plan_pkg/pages/jojo_shop/widget/jojo_shop_item.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home/utils/ext.dart';

class JoJoShopPage extends BasePage {
  //科目类型
  final String subjectType;

  const JoJoShopPage(this.subjectType, {Key? key}) : super(key: key);

  @override
  BaseState<JoJoShopPage> createState() => _JojoShopPageState();
}

class _JojoShopPageState extends BaseState<JoJoShopPage> with BasicInitPage {
  final JoJoShopController _controller = JoJoShopController();

  @override
  Widget body(context) {
    return BlocProvider(
      create: (BuildContext context) => _controller,
      child: BlocBuilder<JoJoShopController, JoJoShopState>(
        builder: (context, state) {
          return AdaptiveOrientationLayout(portrait: (BuildContext context) {
          _controller.isLandscape == false;
          return _buildPageWidget(context);
        }, landscape: (BuildContext context) {
          _controller.isLandscape == true;
          return _buildPageWidget(context);
        });
        },
      ),
    );
  }

  Widget _buildPageWidget(BuildContext context) {
    return JoJoPageLoadingV25(
      scene: PageScene.common,
      hideProgress: true,
      exception: _controller.state.exception,
      retry: () {
        if (mounted) {
          _controller.refresh(widget.subjectType, showLoading: true);
        }
      },
      backWidget: JoJoAppBar(
        backgroundColor: Colors.transparent,
       ),
        status: _controller.state.pageStatus,
        child: Scaffold(
        primary: !JoJoRouter.isWindow,
        appBar: const JoJoAppBar(
            title: "JOJO小铺",
        ),
        body: SafeArea(
          child: ListView.builder(
                padding: EdgeInsets.symmetric(horizontal: 20.rdp),
                itemBuilder: (context, index) {
                  return _buildItem(_controller.state.data?[index]);
                },
                itemCount: _controller.state.data?.length ?? 0,
              ),
        )));
  }

  @override
  void onResume() {
    super.onResume();
    _controller.refresh(widget.subjectType);
  }

  Widget _buildItem(dynamic data) {
    if (data is HeadData) {
      return Container(
          margin: EdgeInsets.only(top: 20.rdp),
          child: JoJoShopHeader(
            data: data,
            segmentList: _controller.state.segmentList??[],
            subjectType: widget.subjectType,
          ));
    } else if (data is ShopData) {
      return Container(
          margin: EdgeInsets.only(top: 20.rdp),
          child: JoJoShopItem(
            data: data,
            segmentList: _controller.state.segmentList??[],
            subjectType: widget.subjectType,
          ));
    } else if (data is BannerData) {
      if(isInIosAuditMode()){
        return Container(); //审核模式处理
      }
      return Container(
          margin: EdgeInsets.only(top: 20.rdp),
          child: JoJoShopBanner(
            data: data,
            segmentList: _controller.state.segmentList??[],
            subjectType: widget.subjectType,
          ));
    }
    return Container();
  }
}
