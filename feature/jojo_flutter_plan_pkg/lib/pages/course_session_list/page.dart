import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/event_bus.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/base_page.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import 'package:jojo_flutter_plan_pkg/pages/course_session_list/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/course_session_list/state.dart';
import 'package:jojo_flutter_plan_pkg/pages/course_session_list/view.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/loading/page.dart';

import '../../common/config/config.dart';
import '../../common/host_env/host_env.dart';
import '../../static/img.dart';
import 'mixins/tablet_scale_minx.dart';
import 'model/course_session_event.dart';
import 'model/route_params.dart';
import 'model/session_utils_info.dart';

class JoJoCourseSessionListPage extends BasePage {
  final String classKey;
  final String lessonKey;
  final int engineType;
  final String? extra;
  final String deviceOrientation;
  final int loadingScene;

  const JoJoCourseSessionListPage(
      {Key? key,
      required this.classKey,
      required this.lessonKey,
      required this.engineType,
      required this.extra,
      required this.deviceOrientation,
      required this.loadingScene})
      : super(key: key);

  @override
  State<JoJoCourseSessionListPage> createState() =>
      _JoJoCourseSessionListPageState();
}

class _JoJoCourseSessionListPageState
    extends BaseState<JoJoCourseSessionListPage>
    with BasicInitPage, TabletScaleMixin {
  late StreamSubscription _eventStepBus;
  late CourseSessionListController _controller;
  final String tag = "CourseSessionListController";

  @override
  void initState() {
    _initSysStatus(true);
    final routeParams = CourseSessionRouteParams(
        engineType: widget.engineType,
        extra: widget.extra,
        deviceOrientation: widget.deviceOrientation,
        loadingScene: widget.loadingScene);
    _controller = CourseSessionListController(
        classKey: widget.classKey,
        lessonKey: widget.lessonKey,
        routeParams: routeParams);
    super.initState();
    listenerStepEnd();
    l.i("StepManager", "JoJoCourseSessionListPage:::initState");
  }

  void listenerStepEnd() {
    _eventStepBus =
        jojoEventBus.on<NativeNotificationStepListEndEvent>().listen((event) {
      _controller.updateStepType(event.stepType);
    });
  }

  @override
  void dispose() {
    super.dispose();
    l.i("StepManager", "JoJoCourseSessionListPage:::dispose");
    _initSysStatus(false);
    _eventStepBus.cancel();
    _controller.pageDispose();
  }

  @override
  void onPause() {
    super.onPause();
    l.d(tag, "onPause");
    jojoEventBus.fire(JoJoCourseSessionListPauseAudioEvent());
  }

  @override
  void onResume() {
    super.onResume();
    l.i("StepManager", "JoJoCourseSessionListPage:::onResume");
    jojoNativeBridge.cleanGameStackAndEnsureSingleSessionList();

    if (_controller.goCourseSettle()) {
      l.i("StepManager", "触发结算流程拦截刷新事件");
      return;
    }
    _controller.refreshLessonList();
  }

  // 设置横屏、状态栏
  void _initSysStatus(bool init) {
    if (init) {
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);
      SystemChrome.setEnabledSystemUIMode(
        SystemUiMode.manual,
        overlays: [],
      );
    } else {
      SystemChrome.setEnabledSystemUIMode(
        SystemUiMode.edgeToEdge,
        overlays: [],
      );
    }
  }

  @override
  Widget body(context) {
    return BlocProvider(
      create: (_) => _controller,
      child: BlocListener<CourseSessionListController, CourseSessionListState>(
        listenWhen: (previous, current) =>
            previous.isGoCourseSettle != current.isGoCourseSettle,
        listener: (context, state) {
          if (RunEnv.isIOS) {
            if (state.isGoCourseSettle) {
              JoJoNativeBridge.shared.setInteractivePopEnable(enable: "0");
            } else {
              JoJoNativeBridge.shared.setInteractivePopEnable(enable: "1");
            }
          }
        },
        child: _buildBody(context),
      ),
    );
  }

  Widget _buildBody(context) {
    return BlocBuilder<CourseSessionListController, CourseSessionListState>(
        builder: (context, state) {
      if (state.isGoCourseSettle) {
        l.i("StepManager", "启用 结算Loading 组件");
        return FinishCourseSettleLoadingPage({
          "classKey": widget.classKey,
          "lessonKey": widget.lessonKey,
          "subjectColor": state.bgColor,
        });
      }
      if (state.status == PageStatus.loading ||
          state.status == PageStatus.error) {
        return Container(
          color: Colors.white,
          child: JoJoPageLoadingV25(
              hideProgress: true,
              scene: PageScene.fromValue(widget.loadingScene) ??
                  PageScene.getDefault(),
              status: state.status,
              retry: () => _controller.refreshLessonList(),
              exception: state.exception,
              backWidget: Padding(
                padding: EdgeInsets.only(
                  left: applyTabletScale(context, 24.rdp),
                  top: applyTabletScale(context, 24.rdp),
                ),
                child: GestureDetector(
                  onTap: () {
                    JoJoRouter.pop();
                    _controller
                        .reportTrack(_controller.appClickEvent, trackMap: {
                      r"$screen_name": "2025改版_环节列表",
                      r"$element_name": "返回_点击",
                    });
                  },
                  child: ImageAssetWeb(
                    assetName: AssetsImg.COURSE_SESSION_LIST_ARROW_YELLOW_BACK,
                    package: Config.package,
                    height: applyTabletScale(context, 51.rdp),
                    width: applyTabletScale(context, 47.rdp),
                    fit: BoxFit.fill,
                  ),
                ),
              ),
              child: Container()),
        );
      }
      return JoJoContainerWidget(
          state: state, loadingScene: widget.loadingScene);
    });
  }
}
