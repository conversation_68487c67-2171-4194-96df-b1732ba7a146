import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/download/jojo_down_gray.dart';
import 'package:jojo_flutter_base/download/jojo_download.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading.dart';
import 'package:jojo_flutter_base/widgets/popup/loading.dart';
import 'package:jojo_flutter_base/widgets/popup/toast.dart';
import 'package:jojo_flutter_plan_pkg/ext/list_ext.dart';
import 'package:jojo_flutter_plan_pkg/ext/string_ext.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/model/course_promote_finish.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/state.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/utils/course_helper.dart';
import 'package:collection/collection.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/utils/dialog_course_helper.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/utils/spine_download_manager.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/utils/course_map_utils.dart';
import 'package:jojo_flutter_plan_pkg/pages/review_detail/model/review_assistant_data.dart';
import '../../generated/l10n.dart';
import '../../service/home_map_lesson_page_api.dart';
import '../plan_home_map/model/course_float_window_clean_data.dart';
import 'event/course_lesson_event.dart';
import 'model/card_course_theme_info.dart';
import 'model/course_lesson_info.dart';
import 'model/course_lesson_reward.dart';

class PlanHomeLessonCtrl extends Cubit<PlanHomeLessonListState> {
  HomeMapLessonPageApi? _lessonPageApi;
  bool needShowCourseHeader;
  bool needShowTodayGuide;
  bool needUpdateActivityData = false;
  bool? isCurrentWidgetShow;
  AbsDownloadManager? resourceManager = JoJoResourceManager();

  PlanHomeLessonCtrl(
      {HomeMapLessonPageApi? api,
      this.needShowCourseHeader = true,
      this.needShowTodayGuide = true})
      : super(PlanHomeLessonListState(
          pageStatus: PageStatus.loading,
        )) {
    _lessonPageApi = api ?? homeMapPageApiService;
    // _lessonPageApi = homeMapPageApiServiceMock;
  }

  int todayIndex = -1; //今的位置
  int defaultIndex = 0; //列表初始化定位
  int offsetIndex = 0; //便宜量，用于计算定位
  int? loadingScene;
  final String defaultColor = "#FCDA00"; //和产品对齐的课时列表兜底颜色
  final Map<int, CourseCard?> _supplements =
      <int, CourseCard?>{}; //缓存用户所有补的坐标数组

  final Map<int?, ReviewEnterDataModel> segmentSupplementMap =
      <int?, ReviewEnterDataModel>{}; //以主题的纬度存储补学数据
  final Map<int?, ReviewEnterDataModel> weekSupplementMap =
      <int?, ReviewEnterDataModel>{}; //以单元的纬度存储补学数据

  CancelToken? _acquireCancelToken;
  bool isHasTodayLesson = false;
  double promoteFinishCardTopSpace = 350.rdp;
  Map<int, CourseTheme?> themesMap = {}; //存储主题数据，用于处理颜色
  Map<int, CourseUnit?> unitsMap = {}; //存储单元数据，用于处理颜色
  Map<String, String> promoteFinishSpineMap =
      {}; //存储已经完成的课程的spine数据，用于防止刷新闪动的问题
  bool isFriendPlayScanAnimation = false; //存储好友卡扫光动画，一个生命周期内只展示一次
  bool? isFoldCourse; //是否折叠课前准备

  String getMainColor() {
    return state.subjectColor ?? defaultColor;
  }

  ///科目列表获取不同级别的color
  Color getColor(BuildContext context, int colorLevel) {
    if (colorLevel == 1) {
      return context.appColors.colorVariant1(HexColor(getMainColor()));
    } else if (colorLevel == 2) {
      return context.appColors.colorVariant2(HexColor(getMainColor()));
    } else if (colorLevel == 3) {
      return context.appColors.colorVariant3(HexColor(getMainColor()));
    } else if (colorLevel == 4) {
      return context.appColors.colorVariant4(HexColor(getMainColor()));
    } else if (colorLevel == 5) {
      return context.appColors.colorVariant5(HexColor(getMainColor()));
    } else if (colorLevel == 6) {
      return context.appColors.colorVariant6(HexColor(getMainColor()));
    }
    return HexColor(getMainColor());
  }

  Future<CourseLessonInfo?> retryRequest(
      Future<CourseLessonInfo?> Function() request,
      {int maxRetries = 3}) async {
    int retryCount = 0;

    while (retryCount <= maxRetries) {
      try {
        return await request();
      } on DioException catch (e) {
        if (e.type == DioExceptionType.connectionTimeout ||
            e.type == DioExceptionType.unknown ||
            e.type == DioExceptionType.receiveTimeout) {
          retryCount++;
          if (retryCount > maxRetries) rethrow;
          l.e("2025课时列表", "请求失败，第 $retryCount 次重试: ${e.message}}");
          continue;
        } else {
          rethrow;
        }
      }
    }
    throw Exception('请求失败，已达到最大重试次数');
  }

  Future<CourseLessonInfo?> loadLessonInfoForPage(
      String classKey, int pageIndex) async {
    _lessonPageApi ??= homeMapPageApiService;
    return retryRequest(() {
      return _lessonPageApi!.getUserClass(classKey, pageIndex);
    }, maxRetries: 3);
  }

  Future<List<TimetableNodeList>> loadLessonInfoAllPage(
      String classKey, int startIndex, int pageCount) async {
    List<Future<List<TimetableNodeList>>> futures = [];
    // 按页码顺序创建 Future 请求
    for (int i = startIndex; i <= pageCount; i++) {
      futures.add(loadLessonInfoForPage(classKey, i)
          .then((result) => result?.timetableNodeList ?? []));
    }
    // 使用 Future.wait 等待所有请求完成
    List<List<TimetableNodeList>> results = await Future.wait(futures);
    // 合并结果，保持页码顺序
    List<TimetableNodeList> lessonList =
        results.expand((list) => list).toList();
    return lessonList;
  }

  /// color 两个字段用于单元测试，因为单元测试无法mock 拓展方法
  Future onRefresh(BuildContext context, String? classKey,
      {Color? color4, Color? color6, Color? colorVariant5}) async {

        final time1 = DateTime.now().millisecondsSinceEpoch;
                      l.d('testIn', " lesson onRefresh start $time1");

    try {
      if (isCurrentWidgetShow == true) {
        jojoEventBus.fire(LessonActivityPicEvent(
            activityPic: state.promoteFinishData?.topImageIconProtrait,
            activityPicPad: state.promoteFinishData?.topImageIconLandscape,
            activityPersonalImage:
                state.promoteFinishData?.topPersonalImage)); //刷新头图
      }
      if (classKey.isNullOrEmpty() || classKey == null) {
        if (state.lessonInfo != null) {
          l.e("2025课时列表", "静默刷新参数异常classKey:$classKey");
          return;
        }
        final newState = state.copyWith()
          ..pageStatus = PageStatus.error
          ..exception = Exception(S.of(context).parameterException);
        emit(newState);
        return;
      }
      CourseLessonInfo? lessonInfo =
          await loadLessonInfoForPage(classKey, 1); //先请求第一页的数据
      if (lessonInfo == null) {
        final newState = state.copyWith()..pageStatus = PageStatus.empty;
        emit(newState);
        return;
      }
      int pageCount = lessonInfo.pageCount ?? 0;
      if (pageCount > 1) {
        //进行批量接口请求，成功后进行数据展示
        List<TimetableNodeList> nodeList =
            await loadLessonInfoAllPage(classKey, 2, pageCount);
        lessonInfo.timetableNodeList?.addAll(nodeList);
      }
      isHasTodayLesson = false;
      isFoldCourse ??= ClassStatus.isLearningClass(lessonInfo.classStatus);
      if (context.mounted) {
        PromoteLessonFinishModel? promoteFinishModel =
            PromoteLessonFinishModel.fromClassTimetableVo(
              context,
                classKey,
                state.promoteFinishData?.lessonInProgress?.completeLessonCount,
                lessonInfo,
                this);
        // 下载动效资源
        _dealWithActivitySpineResources(promoteFinishModel, false);
        CourseListData lessonList = _formatLessonListDate(context, lessonInfo,
            classKey, promoteFinishModel?.preLessonPreviewHeader,
            color4: color4, color6: color6, colorVariant5: colorVariant5);
        final newState = state.copyWith()
          ..pageStatus = _checkLessonListNullEmpty(lessonList.courseList)
              ? PageStatus.empty
              : PageStatus.success
          ..lessonInfo = lessonInfo
          ..promoteFinishData = promoteFinishModel
          ..lessonListData = lessonList
          ..subjectColor = lessonInfo.subjectColor
          ..courseLabel = lessonInfo.courseLabel
          ..userPageCourseLabel = lessonInfo.userPageCourseLabel;
        emit(newState);
      }
      //发送课表数据事件给悬浮窗
      var isTodayFinish = lessonInfo.timetableNodeList
              ?.firstWhereOrNull((item) => item.today == true)
              ?.nodeStatus ==
          CourseCard.lockedFinish;

      var classFunlist = lessonInfo.classFunctionList;

      jojoEventBus.fire(CourseFloatWindowEventData(
          isHasTodayLesson ? isTodayFinish : false,
          classFunlist,
          getMainColor(),
          lessonInfo.subjectType,
          classKey,
          lessonInfo.classId, {
        'course_type': lessonInfo.courseType?.getCourseTypeStr() ?? "",
        'course_stage': lessonInfo.courseSegment ?? "",
        'business_type': lessonInfo.classStatus?.getClassStatusStr() ?? "",
        'material_id': lessonInfo.subjectName ?? "",
        'class_id': lessonInfo.classId ?? "",
      }));
      if (isCurrentWidgetShow == true) {
        jojoEventBus.fire(LessonActivityPicEvent(
            activityPic: state.promoteFinishData?.topImageIconProtrait,
            activityPicPad: state.promoteFinishData?.topImageIconLandscape,
            activityPersonalImage:
                state.promoteFinishData?.topPersonalImage)); //刷新头图
      }
    } catch (e, stack) {
      l.e("2025课时列表", "列表数据异常: $e\n stack=$stack");
      //请求失败
      if (state.lessonInfo != null) {
        //如果接口有数据，那么接口异常不展示异常
        return;
      }
      var exception = Exception("$e");
      if (e is Exception) {
        exception = e;
      }
      final newState = state.copyWith()
        ..pageStatus = PageStatus.error
        ..exception = exception;
      emit(newState);
    } finally {
      DialogCourseHelper().setCourseLessonState(state);
      final time = DateTime.now().millisecondsSinceEpoch;
      final total = time - time1;
      l.d('testIn', " lesson onRefresh $time, total:$total");
    }
  }

  Future retry(BuildContext context, String? classKey) async {
    try {
      final newState = state.copyWith()
        ..pageStatus = PageStatus.loading
        ..exception = Exception(S.of(context).parameterException);
      emit(newState);
      onRefresh(context, classKey);
    } catch (e, stack) {
      l.e("2025课时列表", "列表数据异常-retry: $e\n stack=$stack");
    }
  }

  bool _checkLessonListNullEmpty(List<CoursePlanBase>? courseList) {
    return (courseList?.length == 1) && //列表会始终添加一个占位展示，防止-1计算越位
        (courseList?.firstOrNull is CourseSpaceTop);
  }

  /// 批量领取奖励
  Future<List<Result>> acquireStudyPoints(
      BuildContext context,
      String? classKey,
      int? segmentId,
      int? lessonId,
      List<StudyRewardPoint>? studyReward) async {
    try {
      if (_acquireCancelToken != null) {
        _acquireCancelToken?.cancel();
        _acquireCancelToken = null;
      }
      _acquireCancelToken = _acquireCancelToken ?? CancelToken();
      List<Map<String, dynamic>> requestMap = [];
      studyReward?.forEach((element) {
        requestMap.add({
          "batchId": element.batchId,
          "assetType": element.rewardType,
          "actionType": element.actionType,
          "tradeCount": element.rewardCount,
          "actionTargetId": element.actionTargetId,
          "bizData": {
            "classKey": classKey,
            "segmentId": segmentId,
            "lessonId": lessonId
          }
        });
      });
      final Map<String, dynamic> map = {"reqs": requestMap};
      CourseLessonReward? rewardResult =
          await _lessonPageApi?.acquireStudyPoints(
              map, RequestOptions(cancelToken: _acquireCancelToken));
      //领取成功，刷新接口
      if (context.mounted) {
        onRefresh(context, classKey);
      }
      List<Result> newList =
          rewardResult?.results?.where((e) => e.status == 1).toList() ?? [];
      return Future.value(newList);
    } catch (e, stack) {
      l.e("2025课时列表", "领取奖励异常: $e\n stack=$stack");
      //请求失败
      return Future.value([]);
    } finally {
      _acquireCancelToken = null;
    }
  }

  // 刷新完课活动任务都完成时的数据，并且刷新列表页面
  void refreshPromoteFinishData() {
    final newState = state.copyWith();
    LessonInProgressModel? lessonInProgressModel =
        newState.promoteFinishData?.lessonInProgress;
    if (lessonInProgressModel != null) {
      lessonInProgressModel.preCompleteLessonCount =
          lessonInProgressModel.completeLessonCount;
      newState.promoteFinishData?.lessonInProgress = lessonInProgressModel;
      emit(newState);
    }
  }

  @visibleForTesting
  void testDealWithActivitySpineResources(
      PromoteLessonFinishModel model, bool notNeedCheck) {
    _dealWithActivitySpineResources(model, notNeedCheck);
  }

  void _dealWithActivitySpineResources(
      PromoteLessonFinishModel? model, bool notNeedCheck) {
    if (model?.lessonInProgress?.spineResourceInfo == null) return;

    // 创建资源下载队列
    final resourceQueue = ResourceDownloadQueue(
      firstBatch: [
        model?.lessonInProgress?.spineResourceInfo?.targetResource.resourceUrl
      ].whereNotNull().toList(),
      secondBatch: [
        model?.lessonInProgress?.spineResourceInfo?.badgeBgResource.resourceUrl,
        model?.lessonInProgress?.spineResourceInfo?.starResource.resourceUrl,
        model?.lessonInProgress?.spineResourceInfo?.starFlyResource.resourceUrl,
      ].whereNotNull().toList(),
      thirdBatch: (model?.lessonInProgress?.giftList ?? [])
          .map((e) => e.giftSpineUrl)
          .whereNotNull()
          .toSet()
          .toList(),
    );
    // 开始下载队列
    _processResourceQueue(resourceQueue, notNeedCheck);
  }

  // 处理资源下载队列
  void _processResourceQueue(ResourceDownloadQueue queue, bool notNeedCheck) {
    if (queue.firstBatch.isEmpty) {
      _processSecondBatch(queue, notNeedCheck);
      return;
    }

    downloadZipRes(
      queue.firstBatch,
      successListener: (map) {
        updateResourceState(map, false, (model, kMap) {
          String? targetResource = model
              ?.lessonInProgress?.spineResourceInfo?.targetResource.resourceUrl;
          if (kMap.containsKey(targetResource) || notNeedCheck) {
            model?.lessonInProgress?.spineResourceInfo?.targetResource
                .isDowned = true;
            model?.lessonInProgress?.spineResourceInfo?.targetResource
                .localPath = kMap[targetResource];
            return true;
          }
          return false;
        });

        // 处理下一批资源
        _processSecondBatch(queue, notNeedCheck);
      },
    );
  }

  void _processSecondBatch(ResourceDownloadQueue queue, bool notNeedCheck) {
    if (queue.secondBatch.isEmpty) {
      _processThirdBatch(queue, notNeedCheck);
      return;
    }

    downloadZipRes(
      queue.secondBatch,
      successListener: (map) {
        updateResourceState(map, false, (model, kMap) {
          bool updated = false;

          String? badgeBgResource = model?.lessonInProgress?.spineResourceInfo
              ?.badgeBgResource.resourceUrl;
          if (kMap.keys.contains(badgeBgResource) || notNeedCheck) {
            model?.lessonInProgress?.spineResourceInfo?.badgeBgResource
                .isDowned = true;
            model?.lessonInProgress?.spineResourceInfo?.badgeBgResource
                .localPath = kMap[badgeBgResource];
            updated = true;
          }

          String? starResource = model
              ?.lessonInProgress?.spineResourceInfo?.starResource.resourceUrl;
          if (kMap.keys.contains(starResource) || notNeedCheck) {
            model?.lessonInProgress?.spineResourceInfo?.starResource.isDowned =
                true;
            model?.lessonInProgress?.spineResourceInfo?.starResource.localPath =
                kMap[starResource];
            updated = true;
          }

          String? starFlyResource = model?.lessonInProgress?.spineResourceInfo
              ?.starFlyResource.resourceUrl;
          if (kMap.keys.contains(starFlyResource) || notNeedCheck) {
            model?.lessonInProgress?.spineResourceInfo?.starFlyResource
                .isDowned = true;
            model?.lessonInProgress?.spineResourceInfo?.starFlyResource
                .localPath = kMap[starFlyResource];
            updated = true;
          }

          return updated;
        });

        // 如果所有第二批资源都已下载完成，处理第三批资源
        if (_isSecondBatchComplete()) {
          _processThirdBatch(queue, notNeedCheck);
        }
      },
    );
  }

  void _processThirdBatch(ResourceDownloadQueue queue, bool notNeedCheck) {
    if (queue.thirdBatch.isEmpty) return;

    downloadZipRes(
      queue.thirdBatch,
      successListener: (map) {
        updateResourceState(map, false, (model, kMap) {
          bool refresh = false;
          for (var element in model?.lessonInProgress?.giftList ?? []) {
            if (!element.spineResourceVo.isDowned &&
                (kMap.keys.contains(element.giftSpineUrl) || notNeedCheck)) {
              element.spineResourceVo.isDowned = true;
              element.spineResourceVo.localPath = kMap[element.giftSpineUrl];
              refresh = true;
            }
          }
          // 如果完课数没有变，那么这里需要更新完课卡片的数据，避免卡片使用上一次的数据
          if (model?.lessonInProgress?.preCompleteLessonCount ==
              model?.lessonInProgress?.completeLessonCount) {
            needUpdateActivityData = true;
          }
          return refresh;
        });
      },
    );
  }

  // 更新资源状态并返回是否需要处理下一批资源
  bool updateResourceState(
      Map<String, String> map,
      bool notNeedCheck,
      bool Function(PromoteLessonFinishModel? model, Map<String, String> map)
          updateFunc) {
    final newState = state.copyWith();
    PromoteLessonFinishModel? promoteFinishData = newState.promoteFinishData;

    if (promoteFinishData?.lessonInProgress?.spineResourceInfo == null) {
      return false;
    }
    bool updated = updateFunc(promoteFinishData, map);
    if (updated || notNeedCheck) {
      promoteFinishSpineMap.addAll(map);
      newState.promoteFinishData = promoteFinishData;
      emit(newState);
    }

    return updated;
  }

// 检查第二批资源是否全部下载完成
  bool _isSecondBatchComplete() {
    final newState = state.copyWith();
    PromoteLessonFinishModel? model = newState.promoteFinishData;
    return model?.lessonInProgress?.spineResourceInfo?.badgeBgResource
                .isDowned ==
            true &&
        model?.lessonInProgress?.spineResourceInfo?.starResource.isDowned ==
            true &&
        model?.lessonInProgress?.spineResourceInfo?.starFlyResource.isDowned ==
            true;
  }

  Future<void> downloadZipRes(List<String> urlList,
      {Function(Map<String, String>)? successListener}) async {
    try {
      GlobalDownloadManager downloadManager = GlobalDownloadManager();
      Map<String, String> result = await downloadManager.downloadResources(
        urlList,
        needUnzip: true, // ZIP资源需要解压
      );

      // 调用成功回调
      successListener?.call(result);
    } catch (e) {
      l.e("完课活动", "首页下载Spine资源异常: $e");
    }
  }

  /// 上报查看赠课
  Future lookGiftCourse(
      BuildContext context, String? classKey, int? nodeId) async {
    try {
      await _lessonPageApi?.reportCoursePreviewTaskFinish("$nodeId");
      //领取成功，刷新接口
      if (context.mounted) {
        onRefresh(context, classKey);
      }
    } catch (e, stack) {
      l.e("2025课时列表", "上报接口异常: $e\n stack=$stack");
    }
  }

  /// 上传学习偏好数据
  Future requestStudyPreference(BuildContext context, int? navKey,
      String? classKey, String? toastString) async {
    try {
      final Map<String, dynamic> map = {
        "type": "learning_preference",
        "learningPreference": {
          "classId": state.lessonInfo?.classId ?? 0,
          "courseId": state.lessonInfo?.courseId ?? 0,
          "preferenceType": navKey
        }
      };
      JoJoLoading.show();
      await _lessonPageApi?.requestStudyPreference(map);
      //设置成功，刷新接口
      if (context.mounted) {
        await onRefresh(context, classKey);
      }
      JoJoLoading.dismiss();
      JoJoToast.showSuccess(toastString ?? '学习偏好已保存');
      SmartDialog.dismiss();
    } catch (e, stack) {
      JoJoLoading.dismiss();
      JoJoToast.showSuccess('学习偏好保存失败');
      SmartDialog.dismiss();
      l.e("2025课时列表", "上报接口异常: $e\n stack=$stack");
    }
  }

  /// 视频上报
  Future lockReportVideo(int? type, int? classId, String? bizId) async {
    try {
      UserInfo? info =
          await jojoNativeBridge.getUserInfo().then((value) => value.data);
      final Map<String, dynamic> map = {
        "bizIdList": ["$bizId"],
        "type": type,
        "userId": info?.uid,
        "classId": classId,
      };
      _lessonPageApi?.reportLesson(map);
    } catch (e, stack) {
      l.e("2025课时列表", "视频上报接口异常: $e\n stack=$stack");
    }
  }

  ///原始数据->列表数据
  CourseListData _formatLessonListDate(
      BuildContext context,
      CourseLessonInfo lessonInfo,
      String? classKey,
      PreLessonPreviewHeaderModel? preLessonPreviewHeader,
      {Color? color4,
      Color? color6,
      Color? colorVariant5}) {
    _reset();
    int positionIndex = -1; //定位的位置
    int focusIndex = -1; // 聚焦的位置
    int segmentCount = 0; //主题数
    int weekCount = 0; //单元数
    int loopIndex = 0;
    List<CoursePlanBase> lessonList = <CoursePlanBase>[];
    lessonList.add(CourseSpaceTop()); //添加展位，防止越界
    loopIndex++;

    ///添加提前准备卡片
    if (lessonInfo.courseStartPrepareVo != null && needShowCourseHeader) {
      CourseHeader courseHeader = _addLessonStartReady(
          lessonInfo, classKey, loopIndex, preLessonPreviewHeader);
      lessonList.add(courseHeader); //提前准备
      loopIndex++;
    }

    ///添加主题课时卡片
    if (!lessonInfo.timetableNodeList.isNullOrEmpty()) {
      int? themeSegmentId;
      int? unitWeekId;
      lessonInfo.timetableNodeList?.forEach((item) {
        ///组装主题和标题
        if (item.segmentName.isNotNullOrEmpty()) {
          if (themeSegmentId != item.segmentId) {
            _addTheme(
                context, item, loopIndex, lessonList, classKey, lessonInfo,
                color4: color4); //添加主题
            loopIndex++;
            themeSegmentId = item.segmentId;
            unitWeekId = null; //跨主题后需要清除单元，单元可能相同
            segmentCount++;
          }
          if (item.weekName.isNotNullOrEmpty()) {
            if (unitWeekId != item.weekId) {
              _addUnit(
                  context, item, loopIndex, lessonList, classKey, lessonInfo,
                  color4: color4); //添加单元
              loopIndex++;
              unitWeekId = item.weekId;
              weekCount++;
              //单元上添加引导语
              if (item.introductory.isNotNullOrEmpty()) {
                _addIntroductoryToUnit(item, loopIndex, lessonList, classKey,
                    lessonInfo); //添加单元引导语
                loopIndex++;
              }
            }
          }
        }

        ///课时卡片
        CourseCard lessonCardInfo = CourseHelper().formatCardInfo(
            lessonInfo,
            classKey,
            item,
            needShowTodayGuide,
            lessonInfo.subjectColor ?? defaultColor);
        lessonCardInfo.stickHeaderText = CourseHelper()
            .getCourseStickyHeaderText(
                item.segmentName,
                ClassType.isLooperClass(item.courseChildType)
                    ? item.weekName
                    : null);
        lessonCardInfo.weekId = unitWeekId;
        lessonCardInfo.unFinishedNum = item.unFinishedNum;
        lessonCardInfo.router = item.route;
        lessonCardInfo.type = CourseCardType.TYPE_COURSE_THEME_CARD;
        lessonCardInfo.index = loopIndex;
        lessonCardInfo.courseKey = lessonInfo.courseKey;
        if (item.today == true) {
          todayIndex = loopIndex;
          isHasTodayLesson = true;
        }
        if (item.focus == true) {
          focusIndex = loopIndex;
        }
        if (item.position == true) {
          positionIndex = loopIndex;
        }
        if (item.nodeStatus == CourseCard.supplement) {
          //记录补学数据
          _addSupplementList(loopIndex, lessonCardInfo);
        }
        if (item.nodeStatus != CourseCard.unLocked) {
          //已解锁,恢复颜色
          themesMap[lessonCardInfo.segmentId]?.textColor =
              color6 ?? context.appColors.jColorGray6;
          unitsMap[lessonCardInfo.weekId]?.textColor = colorVariant5 ??
              context.appColors.colorVariant5(
                  HexColor(lessonInfo.subjectColor ?? defaultColor));
        }
        _generaReview(
            lessonBase: lessonCardInfo,
            item: item,
            classKey: classKey,
            loadingScene: loadingScene,
            lessonInfo: lessonInfo);
        lessonList.add(lessonCardInfo); //添加卡片
        loopIndex++;
      });
    }
    //清除今
    if (todayIndex > 0 &&
        lessonList.length > todayIndex &&
        lessonList[todayIndex] is CourseCard) {
      if ((lessonList[todayIndex] as CourseCard).nodeStatus ==
          CourseCard.lockedFinish) {
        todayIndex = -1; //清除今天数据
      }
    }

    ///添加课时预告
    if (lessonInfo.renewalTimetableVo != null) {
      if (lessonInfo.renewalTimetableVo?.userCourseStatus ==
              CourseItemLessonSubscribe.AUTO_SUBSCRIBE ||
          lessonInfo.renewalTimetableVo?.userCourseStatus ==
              CourseItemLessonSubscribe.SUBSCRIBE ||
          lessonInfo.renewalTimetableVo?.userCourseStatus ==
              CourseItemLessonSubscribe.LEARNING_PAY) {
        ///自动订阅，课时预告,先学后付，他们互斥，所以这里只判断一次
        CourseItemLessonSubscribe subscribeInfo =
            _addLessonPreview(lessonInfo, classKey, loopIndex);
        lessonList.add(subscribeInfo); //添加卡片
        loopIndex++;
      }
      if (lessonInfo.renewalTimetableVo?.userCourseStatus ==
          CourseItemFreeze.FREEZE) {
        ///超期退
        CourseItemFreeze freeze =
            CourseItemFreeze(lessonInfo.renewalTimetableVo?.buyCourseDesc);
        freeze.index = loopIndex;
        freeze.type = CourseCardType.TYPE_COURSE_THEME_BACK;
        lessonList.add(freeze); //添加卡片
        loopIndex++;
      }
    }

    ///添加结营按钮
    if (lessonInfo.buyCourseVo != null) {
      CourseLessonBuy buyLesson = CourseLessonBuy(
          lessonInfo.buyCourseVo?.buyCourseSegmentCode,
          lessonInfo.buyCourseVo?.buttonText);
      buyLesson.index = loopIndex;
      buyLesson.type = CourseCardType.TYPE_COURSE_BUY_LESSON;
      buyLesson.router = lessonInfo.buyCourseVo?.route;
      buyLesson.classStatus = lessonInfo.classStatus;
      buyLesson.courseType = lessonInfo.courseType;
      buyLesson.courseSegment = lessonInfo.courseSegment;
      buyLesson.subjectName = lessonInfo.subjectName;
      buyLesson.id = lessonInfo.classId;
      buyLesson.courseKey = lessonInfo.courseKey;
      lessonList.add(buyLesson); //添加卡片
      loopIndex++;
    }
    if (lessonList.length > 1) {
      //添加底部间距
      lessonList.add(CourseSpaceBottom());
      loopIndex++;
    }

    ///处理单个主题单元补展示主题和单元
    if (segmentCount == 1 && weekCount <= 1) {
      //不展示主题和单元
      _resetSegment(lessonList);
    }
    //处理默认定位逻辑，如果有聚焦，那么定位到聚焦，没有聚焦定位到position
    if (focusIndex > -1) {
      defaultIndex = focusIndex; //这里-1是因为有一个空白8.rdp的间距，课时卡片需要
    } else if (positionIndex > -1) {
      defaultIndex = positionIndex;
    }
    defaultIndex = calculateDefaultIndex(defaultIndex, lessonList);
    if (defaultIndex < 0) defaultIndex = 0;
    return CourseListData(lessonList, defaultIndex);
  }

  //需要判断他上一个是否是单元或者标题，否则如果有单元和标题，因为单元标题很宅，会遮挡住部分卡片
  int calculateDefaultIndex(int defaultIndex, List<CoursePlanBase> lessonList) {
    int calculatePosition = defaultIndex;
    if (defaultIndex > 1) {
      //查询聚焦定位的上一个类型
      final planPreBase = lessonList.elementAtOrNull(defaultIndex - 1);
      if (planPreBase is CourseUnit) {
        //单元需要减2个位置，因为单元如果置顶，那么主题会挡住课时定位，需要-2定位到主题，单元在布局上实在主题的下面
        offsetIndex = 2;
        return calculatePosition = defaultIndex - offsetIndex;
      }
      if (planPreBase is CourseTheme ||
          planPreBase is CourseCard ||
          (planPreBase?.stickHeaderText != null &&
              planPreBase?.stickHeaderText?.isNotNullOrEmpty() == true)) {
        //如果是主题和课时活着非课时吸顶，那么只需要-1往上定位
        offsetIndex = 1;
        return calculatePosition = defaultIndex - offsetIndex;
      }
    }
    offsetIndex = 0;
    return calculatePosition;
  }

  //需要判断他上一个是否是单元或者标题，否则如果有单元和标题，因为单元标题很宅，会遮挡住部分卡片
  bool preCardIsThemeOrUnitAtDefaultIndex(
      int defaultIndex, List<CoursePlanBase> lessonList) {
    if (defaultIndex > 1) {
      final planBase = lessonList.elementAtOrNull(defaultIndex - 1);
      if (planBase is CourseTheme || planBase is CourseUnit) {
        return true;
      }
      if (planBase is CourseCard) {
        return planBase.stickHeaderText.isNotNullOrEmpty();
      }
    }
    return false;
  }

  _generaReview(
      {CoursePlanBase? lessonBase,
      TimetableNodeList? item,
      String? classKey,
      int? loadingScene, // 场景ID
      CourseLessonInfo? lessonInfo}) {
    try {
      lessonBase?.unFinishedNum = item?.unFinishedNum;
      lessonBase?.reviewRoute = item?.reviewRoute;
      lessonBase?.weekId = item?.weekId;
      lessonBase?.loadingScene = loadingScene;
      lessonBase?.classKey = classKey;
      lessonBase?.courseKey = lessonInfo?.courseKey;
      lessonBase?.id = lessonInfo?.classId;
      _generaReviewMap(weekSupplementMap, item?.weekId, item, lessonInfo,
          loadingScene, classKey);
      _generaReviewMap(segmentSupplementMap, item?.segmentId, item, lessonInfo,
          loadingScene, classKey);
    } catch (e, stack) {
      l.e("2025课时列表", "_generaReview,列表数据清洗异常: $e\n stack=$stack");
    }
  }

  void _generaReviewMap(
      Map<int?, ReviewEnterDataModel> reviewMap,
      int? id,
      TimetableNodeList? item,
      CourseLessonInfo? lessonInfo,
      int? loadingScene,
      String? classKey) {
    if (id == null || id == 0 || id == -1) {
      return;
    }
    ReviewEnterDataModel? cacheSegmentReviewData = reviewMap[id];
    if (cacheSegmentReviewData == null &&
        item?.reviewRoute?.isNotNullOrEmpty() == true) {
      ReviewEnterDataModel reviewDate = ReviewEnterDataModel();
      reviewDate.segmentId = item?.segmentId;
      reviewDate.weekId = item?.weekId;
      reviewDate.unFinishedNum = item?.unFinishedNum;
      reviewDate.classId = lessonInfo?.classId;
      reviewDate.loadingScene = loadingScene;
      reviewDate.reviewRoute = item?.reviewRoute;
      reviewDate.classKey = classKey;
      reviewMap[id] = reviewDate;
    }
    if (cacheSegmentReviewData != null &&
        (cacheSegmentReviewData.unFinishedNum == null ||
            cacheSegmentReviewData.unFinishedNum != item?.unFinishedNum) &&
        item?.nodeType == LessonCardType.LESSON.statusCode) {
      //做更新
      cacheSegmentReviewData.unFinishedNum = item?.unFinishedNum;
    }
  }

  ///课程预告
  CourseItemLessonSubscribe _addLessonPreview(
      CourseLessonInfo lessonInfo, String? classKey, int loopIndex) {
    List<CourseCard> courseList = <CourseCard>[];
    lessonInfo.renewalTimetableVo?.renewalTimetableList?.forEach((element) {
      courseList.add(CourseHelper().formatCardInfo(
          lessonInfo,
          classKey,
          element,
          needShowTodayGuide,
          lessonInfo.subjectColor ?? defaultColor));
    });
    CourseItemLessonSubscribe subscribeInfo = CourseItemLessonSubscribe(
        lessonInfo.renewalTimetableVo?.userCourseStatus,
        lessonInfo.renewalTimetableVo?.buyCourseDesc,
        lessonInfo.renewalTimetableVo?.route,
        courseList);
    subscribeInfo.index = loopIndex;
    subscribeInfo.type = CourseCardType.TYPE_COURSE_CARD_SUBSCRIBE;
    subscribeInfo.router = lessonInfo.renewalTimetableVo?.route;
    subscribeInfo.classStatus = lessonInfo.classStatus;
    subscribeInfo.courseType = lessonInfo.courseType;
    subscribeInfo.courseSegment = lessonInfo.courseSegment;
    subscribeInfo.subjectName = lessonInfo.subjectName;
    subscribeInfo.id = lessonInfo.classId;
    subscribeInfo.courseKey = lessonInfo.courseKey;
    return subscribeInfo;
  }

  /// 课程准备卡片
  CourseHeader _addLessonStartReady(
      CourseLessonInfo lessonInfo,
      String? classKey,
      int loopIndex,
      PreLessonPreviewHeaderModel? preLessonPreviewHeader) {
    List<CourseGuideService> serviceList = <CourseGuideService>[];
    lessonInfo.courseStartPrepareVo?.prepareNodeList?.forEach((element) {
      String? icon;
      if (element.nodeStatus == CourseCard.unLocked) {
        icon = element.lockIcon;
      } else if (element.nodeStatus == CourseCard.lockedUnFinish) {
        icon = element.unfinishIcon;
      } else if (element.nodeStatus == CourseCard.lockedFinish) {
        icon = element.finishIcon;
      } else {
        icon = element.lockIcon;
      }
      CourseGuideService serviceItem = CourseGuideService(
          element.nodeId,
          lessonInfo.classId,
          classKey,
          element.toast,
          icon,
          element.title,
          element.route,
          element.nodeType,
          element.nodeStatus,
          element.popupInfo,
          CourseHelper().formatGifCourseList(
              getMainColor(), lessonInfo.userGiftCourseVoList));
      serviceItem.classStatus = lessonInfo.classStatus;
      serviceItem.courseType = lessonInfo.courseType;
      serviceItem.courseSegment = lessonInfo.courseSegment;
      serviceItem.subjectName = lessonInfo.subjectName;
      serviceItem.courseStatus = element.nodeStatus;
      serviceItem.id = lessonInfo.classId;
      serviceItem.courseKey = lessonInfo.courseKey;
      serviceList.add(serviceItem);
    });
    CourseHeader courseHeader = CourseHeader(
        lessonInfo.courseStartPrepareVo?.courseImg,
        lessonInfo.courseStartPrepareVo?.title,
        lessonInfo.courseStartPrepareVo?.startClassDateDesc,
        serviceList,
        preLessonPreviewHeader,
        lessonInfo.friendCardInfo,
        isFoldCourse);
    courseHeader.type = CourseCardType.TYPE_COURSE_HEARER_GUIDE;
    courseHeader.index = loopIndex;
    courseHeader.classKey = classKey;
    courseHeader.classStatus = lessonInfo.classStatus;
    courseHeader.courseType = lessonInfo.courseType;
    courseHeader.courseSegment = lessonInfo.courseSegment;
    courseHeader.subjectName = lessonInfo.subjectName;
    courseHeader.id = lessonInfo.classId;
    courseHeader.courseKey = lessonInfo.courseKey;
    return courseHeader;
  }

  void _addTheme(
      BuildContext context,
      TimetableNodeList item,
      int loopIndex,
      List<CoursePlanBase> lessonList,
      String? classKey,
      CourseLessonInfo? lessonInfo,
      {Color? color4}) {
    CourseTheme theme = CourseTheme(item.segmentName, item.segmentId,
        color4 ?? context.appColors.jColorGray4, item.courseChildType == 1);
    theme.type = CourseCardType.TYPE_COURSE_THEME;
    theme.index = loopIndex;
    theme.stickHeaderText = item.segmentName;
    //补读助手，路由需要跳转的参数
    _generaReview(
        lessonBase: theme,
        item: item,
        loadingScene: loadingScene,
        classKey: classKey,
        lessonInfo: lessonInfo);

    lessonList.add(theme); //添加主题
    themesMap[theme.segmentId ?? -1] = theme;
  }

  void _addUnit(
      BuildContext context,
      TimetableNodeList item,
      int loopIndex,
      List<CoursePlanBase> lessonList,
      String? classKey,
      CourseLessonInfo? lessonInfo,
      {Color? color4}) {
    bool isLooperClass = ClassType.isLooperClass(item.courseChildType);
    CourseUnit unit = CourseUnit(
        item.segmentName,
        item.segmentId,
        item.weekName,
        item.weekId,
        color4 ?? context.appColors.jColorGray4,
        isLooperClass);
    unit.type = CourseCardType.TYPE_COURSE_THEME_UNIT;
    unit.index = loopIndex;
    //补读助手，路由需要跳转的参数
    _generaReview(
        lessonBase: unit,
        item: item,
        loadingScene: loadingScene,
        classKey: classKey,
        lessonInfo: lessonInfo);

    unit.stickHeaderText = CourseHelper().getCourseStickyHeaderText(
        item.segmentName, isLooperClass ? item.weekName : null);
    lessonList.add(unit); //添加单元
    unitsMap[unit.unitId ?? -1] = unit;
  }

  void _addIntroductoryToUnit(
    TimetableNodeList item,
    int loopIndex,
    List<CoursePlanBase> lessonList,
    String? classKey,
    CourseLessonInfo? lessonInfo,
  ) {
    bool isLooperClass = ClassType.isLooperClass(item.courseChildType);
    CourseIntroductory introductory = CourseIntroductory(
        item.introductory ?? "",
        item.segmentName,
        item.segmentId,
        item.weekName,
        item.weekId);
    introductory.type = CourseCardType.TYPE_COURSE_THEME_INTRODUCTORY;
    introductory.index = loopIndex;
    introductory.stickHeaderText = CourseHelper().getCourseStickyHeaderText(
        item.segmentName, isLooperClass ? item.weekName : null);
    //补读助手，路由需要跳转的参数
    _generaReview(
        lessonBase: introductory,
        item: item,
        loadingScene: loadingScene,
        classKey: classKey,
        lessonInfo: lessonInfo);
    lessonList.add(introductory); //添加单元引导语
  }

  _resetSegment(List<CoursePlanBase> lessonList) {
    lessonList.removeWhere(
        (element) => element is CourseTheme || element is CourseUnit);
    for (var element in lessonList) {
      if (element is CourseCard) {
        element.segmentName = "";
        element.weekName = "";
      }
    }
  }

  //数据每次刷新需要先清除缓存
  _reset() {
    todayIndex = -1;
    _supplements.clear();
  }

  //添加补学数据
  _addSupplementList(int index, CourseCard? courseInfo) {
    _supplements[index] = courseInfo;
  }

  int getMaxSupplementIndex() {
    int maxKey = _supplements.isNotEmpty
        ? _supplements.keys
            .reduce((currentMax, key) => key > currentMax ? key : currentMax)
        : -1;
    return maxKey;
  }

  setCurrentShow(bool isShow) {
    isCurrentWidgetShow = isShow;
  }

  //找到符合活动条件的第一个未完成的正学课或者待补学的课，然后做定位，如果没找到，则不做定位
  searchFirstUnFinishLessonAndPosition(bool isMultistage) {
    if (isMultistage) {
      var newState = state.copyWith();
      int resetUnFinishIndex = -1;
      int resetSupplementIndex = -1;
      List<CoursePlanBase> list = newState.lessonListData?.courseList ?? [];
      for (int index = 0; index < list.length; index++) {
        var element = list[index];
        if (element is CourseCard) {
          // 是否满足活动条件
          bool isMeet = element.collectStatus ==
              PromoteLessonLessonCardCollectStatus.MEET;
          // 是否是未完成课时
          bool isUnFinish = element.nodeStatus == CourseCard.lockedUnFinish;
          // 是否是待补学课时
          bool isSupplement = element.nodeStatus == CourseCard.supplement;
          if (isMeet && isUnFinish && resetUnFinishIndex == -1) {
            resetUnFinishIndex = index;
          }
          if (isMeet && isSupplement) {
            resetSupplementIndex = index;
          }
        }
      }
      if (resetUnFinishIndex != -1) {
        newState.lessonListResetPosition = resetUnFinishIndex;
      } else if (resetSupplementIndex != -1) {
        newState.lessonListResetPosition = resetSupplementIndex;
      }
      emit(newState);
    } else {
      // 普通活动直接走原有定位逻辑
      resetActionLessonPosition();
    }
  }

  //点击了暑期活动，需要进行定位
  resetActionLessonPosition() {
    try {
      var newState = state.copyWith();
      int resetIndex = -1;
      if (todayIndex != -1) {
        //说明有正学,优先定位到今日正学
        resetIndex = todayIndex;
      }
      if (resetIndex == -1) {
        //寻找最近的补学
        resetIndex = _supplements.keys.isNotEmpty
            ? _supplements.keys
                .reduce((key1, key2) => key1 > key2 ? key1 : key2)
            : -1;
      }
      //需要判断当前最近的正学
      if (resetIndex > -1) {
        newState.lessonListResetPosition = resetIndex;
      } else {
        newState.lessonListResetPosition = defaultIndex;
      }
      emit(newState);
    } catch (e, stack) {
      l.e("2025课时列表", "暑期活动重定位异常: $e\n stack=$stack");
    }
  }

  Future emitFoldRefresh(bool fold) async {
    isFoldCourse = fold;
    emit(state.copyWith());
  }

  @override
  void emit(PlanHomeLessonListState state) {
    try {
      if (isClosed) {
        return;
      }
      super.emit(state);
    } catch (e, stack) {
      l.e("2025课时列表", "emit异常: $e\n stack=$stack");
    }
  }

  @override
  Future<void> close() {
    CourseHelper().releaseAudioPlayer();
    return super.close();
  }
}
