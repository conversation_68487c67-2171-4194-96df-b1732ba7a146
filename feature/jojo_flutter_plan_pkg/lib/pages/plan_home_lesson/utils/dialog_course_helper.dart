import 'package:collection/collection.dart';
import 'package:flutter/foundation.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/config/config.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/dialog/base_dialog_module_helper.dart';
import 'package:jojo_flutter_base/widgets/dialog/base_dialog_widget.dart';
import 'package:jojo_flutter_base/widgets/dialog/dialog_ext.dart';
import 'package:jojo_flutter_base/widgets/dialog/sort_dialog_helper.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/state.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/eventbus/event_bus_location_classkey_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/model/course_map_home_user_info_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/model/course_subject_clean_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/widget/dialogs/course_promote_finish_guide_dialog.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/widget/dialogs/dialogs_message.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/widget/dialogs/find_study_partner_dialog.dart';

import '../../../page.dart';
import '../../../service/home_map_page_api.dart';
import '../../../service/list_course_card_api.dart';
import '../../plan_auto_transform/model/plan_auto_cms_data.dart';
import '../../plan_home/dailytask/dialog/course_dialog_helper.dart';
import '../../plan_home/model/course_home_page_data.dart';
import '../../plan_home_map/controller.dart';
import '../../plan_home_map/model/course_map_home_page_tab_data.dart';
import '../../plan_home_map/state.dart';
import '../../plan_home_map/widget/dialogs/cms_2025_dialog.dart';
import '../../plan_home_map/widget/dialogs/course_promote_finish_animation_dialog.dart';
import '../../plan_home_map/widget/dialogs/home_incentive_dialog_widget.dart';
import '../../plan_home_map/widget/dialogs/home_personal_image_guide_dialog.dart';
import '../../plan_home_map/widget/dialogs/new_course_guide_dialog.dart';
import '../../plan_home_map/widget/dialogs/plan_add_teacher_2025_dialog.dart';
import '../../plan_home_map/widget/dialogs/train_add_teacher_2025_dialog.dart';
import '../model/course_promote_finish.dart';
import 'course_helper.dart';

///缓存当前弹窗，这里需要校验课时列表数据和首页的弹窗数据，同时返回才构建弹窗序列，需要维护好弹窗序列，否则会导致弹窗数据异常或者弹不出
class DialogCourseHelper {
  DialogCourseHelper._internal();

  static String tag = "UniformDialogSequenceTag";
  static final DialogCourseHelper _instance = DialogCourseHelper._internal();

  factory DialogCourseHelper() => _instance;

  PlanHomeLessonListState? _courseLessonState;
  PlanHomeMapViewState? _state;

  PlanHomeLessonListState? get getLessonState => _courseLessonState;

  PlanHomeMapViewState? get getTabState => _state;

  int? _subjectType;

  ///能够获取 最新的数据
  PlanHomeWithMapviewCtrl? _ctrl;

  Function(int? subjectType, String? courseKey)? _positionToSubjectCallBack;
  Function()? _refreshTopIncentiveData;

  void setCourseLessonState(PlanHomeLessonListState? data) {
    _courseLessonState = data;
    _checkLessonSuccess();
  }

  void setController(PlanHomeWithMapviewCtrl? ctrl, int subjectType) {
    _ctrl = ctrl;
    _subjectType = subjectType;
  }

  void setCourseTab(PlanHomeMapViewState? data, positionToSubjectCallBack,
      refreshTopIncentiveData) {
    _state = data;
    _positionToSubjectCallBack = positionToSubjectCallBack;
    _refreshTopIncentiveData = refreshTopIncentiveData;
  }

  void _checkLessonSuccess() {
    _triggerDialogs();
  }

  /// 所有弹窗需要的数据准备好后，再触发
  _triggerDialogs() async {
    SortDialogHelper()
        .notifyNativeStopAlertSequence(AppPage.planHomeMapPage.path);
    final List<BaseDialogWidget> _dialogSequenceList = [];
    //促完课弹窗相关
    await _addPromoteDialog(_dialogSequenceList, _courseLessonState);
    //个人形象弹窗相关
    await _addIncentiveDialog(
        _dialogSequenceList,
        _ctrl,
        _refreshTopIncentiveData,
        BaseConfig.share.deviceInfo?.grayLandscape == "1",
        BaseConfig.share.userInfo,
        _subjectType);
    // 新课获取引导弹窗
    ClassPagePopupList? pagePopupInfo =
        NewCourseGetGuideWidgetDialog.getNewCourseOpenInfo(
            _state?.tabData?.classPagePopupList);
    bool showNewCourseGuide =
        await NewCourseGetGuideWidgetDialog.shouldShowPOPGuide(
            pagePopupInfo, (_state?.currentSubjectData?.subjectType ?? 2));
    _dialogSequenceList.add(NewCourseGetGuideWidgetDialog(
        canShow: showNewCourseGuide,
        pagePopupInfo: pagePopupInfo,
        subjectName: _state?.currentSubjectData?.title,
        subjectType: _state?.currentSubjectData?.subjectType,
        positionToSubjectCallBack: (subjectType) {
          _positionToSubjectCallBack?.call(subjectType, null);
        },
        onDialogShow: () {},
        onDialogDismiss: () {}));

    /// 训练营添加指导师弹窗 TrainAddTeacher2025Dialog
    List<ClassPagePopupList> classPagePopupList =
        _ctrl?.state.tabData?.classPagePopupList ?? [];
    BaseUserClassListCleanData? currentCourse = _ctrl?.getCurrentCourse();
    SchedulePopup? trainSchedulePopup = await CourseTrainAddTeacherDialogHelper
        .findCanAutoShowClassPagePopupData(
            classPagePopupList, null, currentCourse?.classId);
    if (trainSchedulePopup != null && currentCourse != null) {
      l.d(tag, "训练营加老师弹窗 存在可弹出数据");
      const screenName = "2025改版后学习页";
      _dialogSequenceList.add(
        TrainAddTeacher2025Dialog(trainSchedulePopup, () {
          return true;
        }, screenName, "训练营加老师弹窗"),
      );
    }

    bool canShowPersonalImageGuide = _ctrl?.isShowIncentiveDialog ?? false;
    _dialogSequenceList.add(HomePersonalImageGuideDialog(
        canShow: canShowPersonalImageGuide,
        params: PersonnalImageGuideDialogParams(
            activityPersonalImage: _ctrl?.state.activityPersonalImage,
            localPersonalImagePath: _ctrl?.state.localPersonalImagePath,
            dressImgUrl: _ctrl?.state.topUserInfo?.dress?.img,
            personalImageRect: _ctrl?.personalImageRect),
        onDialogShow: () {
          _ctrl?.isShowIncentiveDialog = false;
        }));

    /// 系统包添加指导师弹窗 PlanAddTeacher2025Dialog
    var planTeacherDailogData = _state?.tabData?.classPagePopupList
        ?.firstWhereOrNull((popup) => popup.popupType == 3);
    if (planTeacherDailogData != null) {
      var planSchedulePopup = SchedulePopup(
        classId: planTeacherDailogData.classId,
        template: planTeacherDailogData.template,
        teacherProfileUrl: planTeacherDailogData.teacherProfileUrl,
        title: planTeacherDailogData.popupTitle,
        subTitle: planTeacherDailogData.subTitle,
        tags: planTeacherDailogData.tags,
        addTeacherUrl: planTeacherDailogData.addTeacherUrl,
        addButton: planTeacherDailogData.buttonText,
        cancelButton: planTeacherDailogData.cancelButtonText,
      );

      final canPlanShow = !(await CourseTrainAddTeacherDialogHelper
          .canShowAddTeacherDialogCoursePlan(
              "${planTeacherDailogData.classId}"));
      planCanShow() => canPlanShow;

      _dialogSequenceList.add(
        PlanAddTeacher2025Dialog(
          planSchedulePopup,
          planCanShow,
        ),
      );
    }
    final topUser = _ctrl?.state.topUserInfo;
    _dialogSequenceList.add(FindStudyPartnerDialog(
        dataCallback: () async {
          if (topUser == null) {
            return false;
          }
          return await checkStudyPartnerDialogShow(topUser);
        },
        coverImgUrl: topUser?.partnerGuide?.coverImg ?? "",
        router: topUser?.partnerGuide?.jumpRoute ?? "",
        onDialogShow: () {
          bool isLandSpace = BaseConfig.share.deviceInfo?.grayLandscape == "1";
          if (isLandSpace == false) {
            jojoNativeBridge.showHomePageTabs(show: false);
          }
        },
        onDialogDismiss: () {
          ModuleDialogHelper.getInstance().dismissWithTag();
          jojoNativeBridge.showHomePageTabs(show: true);
        }));
    //CMS弹窗
    await _addCMSDialog(
        _dialogSequenceList, BaseConfig.share.deviceInfo?.grayLandscape == "1");
    SortDialogHelper().dialogSequenceList = _dialogSequenceList;
    SortDialogHelper()
        .notifyNativeInitAlertSequence(AppPage.planHomeMapPage.path);
    SortDialogHelper().notifyNativePageReady(AppPage.planHomeMapPage.path);
  }

  ///促完课活动弹窗
  _addPromoteDialog(List<BaseDialogWidget> dialogSequenceList,
      PlanHomeLessonListState? courseLessonState) async {
    // 促完课动画执行弹窗
    PromoteLessonFinishModel? animationVo = courseLessonState?.promoteFinishData;
    LessonInProgressModel? lessonInProgress = animationVo?.lessonInProgress;
    bool animationVoPopShouldShow = false;
    if (animationVo != null && lessonInProgress != null) {
      // 设置执行弹窗的弹出条件（数据有变化才能弹出来）
      int preCompleteCount = lessonInProgress.preCompleteLessonCount;
      int completeLessonCount = lessonInProgress.completeLessonCount;
      bool isAllDowned =
          lessonInProgress.spineResourceInfo?.getDownStatus() ?? false;
      animationVoPopShouldShow =
          completeLessonCount != 0 && preCompleteCount < completeLessonCount;
      if (animationVoPopShouldShow) {
        if (isAllDowned) {
          dialogSequenceList.add(CoursePromoteFinishAnimationDialog(
            canShow: true,
            subjectColor: _ctrl?.state.currentSubjectData?.color,
            onDialogShow: () {},
            onDialogDismiss: () {},
            model: animationVo,
          ));
        } else {
          // 数据变了,但是动效资源没有下载下来,则直接更新卡片上的数据
          jojoEventBus.fire(EventBusPromoteDialogShowData(AnimationDialogType.updateCompleteCountNoAnimation, 0, 0, 0, completeLessonCount));
        }
      }
    }
    if (animationVo != null && !animationVoPopShouldShow) {
      // 促完课行课期引导弹窗(对象非空，并且没有弹出过，则弹出弹窗)
      bool activityPopShouldShow =
          await CoursePromoteFinishGuideWidgetDialog.shouldShowPOPGuide(
              animationVo.activityStartPopupInfo, animationVo.activityId ?? 0);
      if (activityPopShouldShow) {
        dialogSequenceList.add(CoursePromoteFinishGuideWidgetDialog(
          canShow: true,
          onDialogShow: () {},
          onDialogDismiss: () {},
          popupInfo: animationVo.activityStartPopupInfo,
        ));
      }
    }
  }

  ///个人形象弹窗类型修改
  _addIncentiveDialog(
      List<BaseDialogWidget> dialogSequenceList,
      PlanHomeWithMapviewCtrl? ctrl,
      Function()? refreshTopIncentiveData,
      bool isLandSpace,
      UserInfo? userInfo,
      int? subjectType) async {
    dialogSequenceList.add(
      IncentiveDialog(
        onDialogShow: () {
          if (isLandSpace == false) {
            jojoNativeBridge.showHomePageTabs(show: false);
          }
          homeMapPageApiService.requestTopUserInfoDialogCallback(
              ["${userInfo?.uid}"],
              70,
              "${userInfo?.uid}",
              0);
          ctrl?.isShowIncentiveDialog = true;
        },
        onDialogDismiss: () {
          jojoNativeBridge.showHomePageTabs(show: true);
          refreshTopIncentiveData?.call();
        },
        dataCallback: () async {
          return homeMapPageApiService.requestTopUserInfo(subjectType ?? -1);
        },
      ),
    );
  }

  ///CMS弹窗
  _addCMSDialog(
      List<BaseDialogWidget> dialogSequenceList, bool isLandSpace) async {
    dialogSequenceList.add(Cms2025Dialog(
        dataCallback: () async {
          return LessonListServiceBase.requestCMSadInfo('START_PAGE_25');
        },
        pagePath: DialogsMessage.cmsDialog.pagePath,
        dialogKey: DialogsMessage.cmsDialog.key,
        dialogSort: DialogsMessage.cmsDialog.sort,
        dialogType: DialogsMessage.cmsDialog.dialogType,
        onDialogShow: () {
          if (isLandSpace == false) {
            jojoNativeBridge.showHomePageTabs(show: false);
          }
        },
        onDialogDismiss: () {
          SmartDialog.dismiss();
          jojoNativeBridge.showHomePageTabs(show: true);
        }));

    dialogSequenceList.add(Cms2025Dialog(
        pagePath: DialogsMessage.cmsDialog.pagePath,
        dialogKey: 'CmsWithCourseStateDialog',
        dialogSort: DialogsMessage.cmsDialog.sort,
        dialogType: DialogsMessage.cmsDialog.dialogType,
        dataCallback: () async {
          PlanAutoCmsData? popupsInfo;
          final localCourseData = await CourseHelper().getLocalCourseData();
          if (localCourseData != null) {
            try {
              final classId = localCourseData['classId'];
              final lessonId = localCourseData['lessonId'];
              final segmentId = localCourseData['segmentId'];
              popupsInfo = await LessonListServiceBase.requestPopupsInfo(
                  2, 'START_PAGE_25', classId, segmentId, lessonId);
            } catch (e) {
              l.e(tag, 'requestPopupsInfo error: $e');
            }
          }
          return popupsInfo;
        },
        onDialogShow: () {
          if (isLandSpace == false) {
            jojoNativeBridge.showHomePageTabs(show: false);
          }
        },
        onDialogDismiss: () {
          jojoNativeBridge.showHomePageTabs(show: true);
        }));
  }

  Future<bool> checkStudyPartnerDialogShow(TopUserInfo topUser) async {
    bool canShow = (topUser.continuous?.bestDays ?? 0) >= 3;
    if (!canShow) {
      return false;
    }

    var info = await jojoNativeBridge.getUserInfo().then((value) => value.data);
    //海外用户，屏蔽学伴引导弹窗
    if (info?.regionCode != "CN") {
      return false;
    }

    if (topUser.partnerGuide?.coverImg == null) {
      return false;
    }
    if (kDebugMode) {
      return true;
    }
    const localKey = "flutter_studty_partner_key";
    NativeValue? nativeValue = await jojoNativeBridge
        .operationNativeValueGet(key: localKey)
        .then((value) => value.data);

    // 如果已经显示过了，就不再显示
    if (nativeValue?.value == "1") {
      return false;
    }

    // 设置标记表示已经显示过
    await jojoNativeBridge.operationNativeValueSet(key: localKey, value: "1");
    return true;
  }

  Future<bool> showAddTeacherDialog(int popupType, String reason) async {
    var dialogData = _state?.tabData?.classPagePopupList
        ?.firstWhereOrNull((popup) => popup.popupType == popupType);
    if (dialogData != null) {
      SchedulePopup schedulePopup = SchedulePopup(
        classId: dialogData.classId,
        template: dialogData.template,
        icon: dialogData.icon,
        title: dialogData.popupTitle,
        subTitle: dialogData.subTitle,
        tags: dialogData.tags,
        addTeacherUrl: dialogData.addTeacherUrl,
        addButton: dialogData.buttonText,
        cancelButton: dialogData.cancelButtonText,
        popupType: dialogData.popupType,
        subjectTypeDesc: dialogData.subjectTypeDesc,
        courseSegment: dialogData.courseSegment,
        userCourseBusinessStatus: dialogData.userCourseBusinessStatus,
        courseKey: dialogData.courseKey,
        limitFlow: dialogData.limitFlow,
      );

      NativeValue? nativeValue = await jojoNativeBridge
          .showAlertInManager(
              pagePath: AppPage.planHomeMapPage.path,
              dialogKey: "manualShowAddTeacherDialog")
          .then((value) => value.data);

      String? result = nativeValue?.value;
      if (result == "1") {
        SortDialogHelper().dialogShowExecutor?.call(
              true,
              TrainAddTeacher2025Dialog(
                schedulePopup,
                () => true,
                "2025改版后学习页",
                "学习日报加老师弹窗",
                showBigImage: true,
                reason: reason,
                dismissAction: () {
                  jojoNativeBridge.notifyAlertManageAlertDidDismiss(
                      pagePath: AppPage.planHomeMapPage.path,
                      dialogKey: "manualShowAddTeacherDialog");
                },
              ),
            );
        return true;
      }
    }

    return false;
  }
}
