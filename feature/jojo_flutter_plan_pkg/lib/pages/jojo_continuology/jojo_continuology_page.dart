import 'dart:async';

import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/app_bars/appbar_left.dart';
import 'package:jojo_flutter_base/widgets/app_bars/jojo_appbar.dart';
import 'package:jojo_flutter_base/widgets/common/adaptive_orientation_layout.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/pages/jojo_continuology/calendar_event.dart';
import 'package:jojo_flutter_plan_pkg/pages/jojo_continuology/calendar_utils.dart';
import 'package:jojo_flutter_plan_pkg/pages/jojo_continuology/jojo_continuology_controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/jojo_continuology/model/jojo_continuology_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/jojo_continuology/model/jojo_continuology_milestone_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/jojo_continuology/model/jojo_continuology_state.dart';
import 'package:jojo_flutter_plan_pkg/pages/jojo_continuology/util/study_guide_audio_impl.dart';
import 'package:jojo_flutter_plan_pkg/pages/jojo_continuology/widget/calendar_content.dart';
import 'package:jojo_flutter_plan_pkg/pages/jojo_continuology/widget/jojo_continuology_detail_header.dart';
import 'package:jojo_flutter_plan_pkg/pages/jojo_continuology/widget/jojo_continuology_detail_milestone.dart';
import 'package:jojo_flutter_plan_pkg/service/jojo_continuology_api.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';

///连续学
class JoJoContinuologyPageModel extends BasePage {
  //科目类型
  final String subjectType;
  final int? loadingScene;

  const JoJoContinuologyPageModel(this.subjectType, this.loadingScene,
      {super.key});

  @override
  State<StatefulWidget> createState() {
    return _JoJoContinuologyPageState();
  }
}

class _JoJoContinuologyPageState extends BaseState<JoJoContinuologyPageModel>
    with BasicInitPage {
  final JoJoContinuologyController _controller =
      JoJoContinuologyController(StudyGuideAudioImpl(AudioPlayer()));
  final ScrollController _scrollController = ScrollController();
  StreamSubscription? _toolUseAnimationEvent;
  double _milestoneModuleHeight = 0;

  @override
  void initState() {
    super.initState();
    _toolUseAnimationEvent =
        jojoEventBus.on<CalenderAnimationEvent>().listen((event) {
      if (mounted) {
        if (event.scrollListToCalendar) {
          // 滚动列表到日历
          _scrollToIndex();
        }
      }
    });
  }

  void _scrollToIndex() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        _scrollController.position.minScrollExtent +
            162.rdp +
            _milestoneModuleHeight,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _restoreDefaultsBeforeBuild() {
    _milestoneModuleHeight = 0;
  }

  Widget _buildPageWidget(BuildContext context) {
    _restoreDefaultsBeforeBuild();

    return JoJoPageLoadingV25(
        scene:
            PageScene.fromValue(widget.loadingScene ?? 1) ?? PageScene.common,
        exception: _controller.state.exception,
        retry: () {
          if (mounted) {
            _controller.refresh(widget.subjectType, showLoading: true);
          }
        },
        hideProgress: true,
        backWidget: JoJoAppBar(
          backgroundColor: Colors.transparent,
        ),
        status: _controller.state.pageStatus,
        child: Scaffold(
          primary: !JoJoRouter.isWindow,
          appBar: JoJoAppBar(
            title: _controller.state.pageStatus == PageStatus.loading
                ? ""
                : "${_controller.subjectName}${S.of(context).consecutiveWin}",
            actions: [
              GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () {
                  if (_controller.dataModel?.ruleDescRoute != null) {
                    RunEnv.jumpLink(_controller.dataModel?.ruleDescRoute ?? "");
                  }
                },
                child: Container(
                  alignment: Alignment.center,
                  height: 44.rdp,
                  padding: EdgeInsets.only(right: 10.rdp),
                  child: _buildRuleDesc(),
                ),
              )
            ],
          ),
          body: ListView.builder(
            itemBuilder: (context, index) {
              return buildItem(index);
            },
            padding: const EdgeInsets.all(0),
            itemCount: _controller.state.data?.length ?? 0,
          ),
        ));
  }

  Widget _buildRuleDesc() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        ImageAssetWeb(
            assetName: AssetsImg.PLAN_CONTINUOLOGY_EXPLANATION_ICON,
            width: 20.rdp,
            height: 20.rdp,
            package: Config.package),
        SizedBox(width: 2.rdp),
        Text(
          S.of(context).ruleDesc,
          style: TextStyle(
            fontSize: 16.rdp,
            fontWeight: FontWeight.w400,
            color: context.appColors.jColorGray4,
          ),
        )
      ],
    );
  }

  Widget _portrait(BuildContext context) {
    _controller.isLandscape = false;
    return _buildPageWidget(context);
  }

  Widget _landscape(BuildContext context) {
    _controller.isLandscape = true;
    return _buildPageWidget(context);
  }

  @override
  Widget body(context) {
    return BlocProvider(
      create: (BuildContext context) => _controller,
      child: BlocBuilder<JoJoContinuologyController, JoJoContinuologyState>(
        builder: (context, state) {
          l.i("连续学详情", "数据长度 ${_controller.state.data?.length}");
          return AdaptiveOrientationLayout(
              portrait: _portrait, landscape: _landscape);
        },
      ),
    );
  }

  Widget buildItem(int index) {
    dynamic itemData = _controller.state.data?[index];
    l.i("连续学详情",
        "数据长度  ${_controller.state.data?.length} index $index  ${itemData.runtimeType}");
    if (itemData is JoJoContinuologyHeaderDetailDate) {
      return JoJoContinuologyDetailHeader(
          key: ValueKey("${itemData.status}"), data: itemData);
    }
    if (itemData is JojoContinuologyMilestoneData) {
      _milestoneModuleHeight = JoJoContinuologyDetailMilestone.spacing.rdp * 2 +
          JoJoContinuologyDetailMilestone.height.rdp;
      return JoJoContinuologyDetailMilestone(milestoneData: itemData);
    }
    if (itemData is CalendarContentVo) {
      return CalendarContentWidget(calendarVo: itemData);
    }
    return Container();
  }

  @override
  void onResume() {
    super.onResume();
    _controller.refresh(widget.subjectType, showLoading: false);
  }

  @override
  void onPause() {
    _controller.disposeAudioPlayer();
    _controller.stopStudyGuideAudio();
    super.onPause();
  }

  @override
  void dispose() {
    _controller.disposeAudioPlayer();
    _controller.disposeGuideAudio();
    _controller.disposeGuideCountDown();
    _toolUseAnimationEvent?.cancel();
    _toolUseAnimationEvent = null;
    super.dispose();
  }
}
