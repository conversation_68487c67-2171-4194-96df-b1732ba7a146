import 'package:flutter/foundation.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import 'package:jojo_flutter_plan_pkg/pages/my_partners/state.dart';
import 'package:jojo_flutter_plan_pkg/service/my_partners_api.dart';
import 'package:jojo_flutter_plan_pkg/pages/my_partners/model/models.dart';
import 'package:jojo_flutter_plan_pkg/pages/my_partners/mappers/my_partners_mapper.dart';
import 'package:jojo_flutter_plan_pkg/pages/my_partners/view_models/my_partners_view_models.dart';
import 'package:jojo_flutter_plan_pkg/pages/my_partners/enums/dynamic_action_type.dart';

import '../../service/partner_apply_api.dart';

class MyPartnersCtrl extends Cubit<MyPartnersState> {
  final String _logTag = "MyPartnersCtrl";
  final MyPartnersApi _myPartnersApi;

  RefreshController refreshDynamicController =
      RefreshController(initialRefresh: false);

  RefreshController refreshPartnerController =
      RefreshController(initialRefresh: false);

  MyPartnersCtrl({MyPartnersApi? api})
      : _myPartnersApi = api ?? myPartnersApi,
        super(
          MyPartnersState(pageStatus: PageStatus.loading),
        ) {
    initState();
    l.i(_logTag, 'MyPartnersCtrl initialized');
  }

  // 切换动态/学伴标签
  changeTab(int index) {
    final newState = state.copyWith(selectedTabIndex: index);
    emit(newState);

    // 如果是首次切换到学伴列表且列表为空，则加载数据
    if (index == 1 && state.partnersList.isEmpty) {
      loadPartners(refresh: true);
    }
  }

  initState() async {
    try {
      // 并行加载数据
      await Future.wait([
        loadDiscoverPartners(),
        loadDynamics(refresh: true),
        loadPartners(refresh: true)
      ]);

      final newState = state.copyWith(pageStatus: PageStatus.success);
      emit(newState);

      if (!newState.hasMoreDynamics) {
        refreshDynamicController.loadNoData();
      }

      if (!newState.hasMorePartners) {
        refreshPartnerController.loadNoData();
      }
    } catch (e) {
      l.i(_logTag, 'MyPartnersCtrl initState error: $e');
      final newState = state.copyWith(pageStatus: PageStatus.error);
      emit(newState);
    }
  }

  // 加载发现学伴数据
  Future<void> loadDiscoverPartners() async {
    try {
      final DiscoverPartnersData? response =
          await _myPartnersApi.getDiscoverPartners();
      if (response != null) {
        final viewModel = MyPartnersMapper.mapDiscoverPartnersData(response);
        final newState = state.copyWith(
          discoverPartnersData: viewModel,
          learningPartnersCount: viewModel.partnerList.length,
        );
        emit(newState);
      }
    } catch (e) {
      l.i(_logTag, 'loadDiscoverPartners error: $e');
    }
  }

  // 加载学伴数量（用于标题显示）
  Future<void> loadLearningPartnersCount() async {
    try {
      final response = await _myPartnersApi.getLearningPartners(1, 1);
      final newState = state.copyWith(
        learningPartnersCount: response?.partners?.length ?? 0,
      );
      emit(newState);
    } catch (e) {
      l.i(_logTag, 'loadLearningPartnersCount error: $e');
    }
  }

  // 加载动态列表
  Future<void> loadDynamics({bool refresh = false}) async {
    try {
      if (refresh) {
        final newState = state.copyWith(
          nextDynamicsMinId: 0,
          dynamicsList: [],
        );
        emit(newState);
      }

      final response = await _myPartnersApi.getUserDynamics(
          state.nextDynamicsMinId, state.pageSize);

      if (response != null) {
        final viewModel = MyPartnersMapper.mapDynamicsListData(response);
        List<DynamicViewModel> newList = [];
        if (refresh) {
          newList = viewModel.dynamics;
        } else {
          newList = [...state.dynamicsList, ...viewModel.dynamics];
        }

        final newState = state.copyWith(
          dynamicsList: newList,
          nextDynamicsMinId: response.minId,
          hasMoreDynamics: viewModel.dynamics.length >= state.pageSize,
        );
        emit(newState);
      }
    } catch (e) {
      l.i(_logTag, 'loadDynamics error: $e');
    }
  }

  int partnersPageSize = 20;

  // 加载学伴列表
  Future<void> loadPartners({bool refresh = false}) async {
    try {
      if (refresh) {
        final newState = state.copyWith(
          nextPartnersMinId: 0,
          partnersList: [],
        );
        emit(newState);
      }

      final response = await _myPartnersApi.getLearningPartners(
          state.nextPartnersMinId, partnersPageSize);

      if (response != null) {
        final viewModel = MyPartnersMapper.mapPartnersListData(response);
        List<PartnerViewModel> newList = [];
        if (refresh) {
          newList = viewModel.partners;
        } else {
          newList = [...state.partnersList, ...viewModel.partners];
        }

        final newState = state.copyWith(
          partnersList: newList,
          nextPartnersMinId: response.minId,
          hasMorePartners: viewModel.partners.length >= partnersPageSize,
        );
        emit(newState);
      }
    } catch (e) {
      l.i(_logTag, 'loadPartners error: $e');
    }
  }

  // 发送互动（送花花/戳一戳）
  Future<void> sendAction(DynamicActionType actionType, int dynamicRelationId,
      int partnerId) async {
    try {
      final request = DynamicsActionRequest(
        action: actionType.value,
        dynamicRelationId: dynamicRelationId,
        partnerId: partnerId,
      );

      l.d(_logTag, 'sendAction flower');

      if (kDebugMode) {
        Future.delayed(const Duration(milliseconds: 200));
      } else {
        await _myPartnersApi.sendDynamicsAction(request);
      }

      // 更新本地状态
      final updatedList = state.dynamicsList.map((item) {
        if (item.dynamicId == dynamicRelationId) {
          final newActionState = actionType == DynamicActionType.poke
              ? item.actionState.copyWith(hasPoked: true)
              : item.actionState.copyWith(hasGivenFlower: true);
          return item.copyWith(actionState: newActionState);
        }
        return item;
      }).toList();

      final newState = state.copyWith(dynamicsList: updatedList);
      emit(newState);
    } catch (e) {
      l.i(_logTag, 'sendAction error: $e');
      // 可以显示错误提示
    }
  }

  // 动态列表下拉刷新
  Future<void> onDynamicsRefresh() async {
    await loadDynamics(refresh: true);
    refreshDynamicController.refreshCompleted();
    if (state.hasMoreDynamics) {
      refreshDynamicController.resetNoData();
    } else {
      refreshDynamicController.loadNoData();
    }
  }

  // 动态列表上拉加载更多
  Future<void> onDynamicsLoadMore() async {
    if (state.hasMoreDynamics) {
      await loadDynamics();
      refreshDynamicController.loadComplete();
      if (!state.hasMoreDynamics) {
        refreshDynamicController.loadNoData();
      }
    }
  }

  // 学伴列表下拉刷新
  Future<void> onPartnersRefresh() async {
    await loadPartners(refresh: true);
    refreshPartnerController.refreshCompleted();
    if (state.hasMorePartners) {
      refreshPartnerController.loadComplete();
    } else {
      refreshPartnerController.loadNoData();
    }
  }

  // 学伴列表上拉加载更多
  Future<void> onPartnersLoadMore() async {
    if (state.hasMorePartners) {
      await loadPartners();
      refreshPartnerController.loadComplete();
      if (!state.hasMorePartners) {
        refreshPartnerController.loadNoData();
      }
    }
  }

  /// 删除学伴
  Future<void> deletePartner(int partnerId) async {
    List<PartnerViewModel> list = [];
    for (var element in state.partnersList) {
      if (element.partnerId != partnerId) {
        list.add(element);
      }
    }
    emit(state.copyWith(partnersList: list));
    await partnerApplyApi.handleApply(partnerId, {'action': 'cancel'});
    if (list.length < 10 && state.hasMorePartners) {
      onPartnersLoadMore();
    }
  }
}
