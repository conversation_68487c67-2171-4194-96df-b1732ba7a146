import 'dart:math';

import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_animation_control.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_animation_widget.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/svg_asset.dart';
import 'package:jojo_flutter_base/widgets/visibility_observe.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/incentive_module/home_incentive_info_widget.dart';

import '../../../common/config/config.dart';
import '../../../common/host_env/host_env.dart';
import '../../../generated/l10n.dart';
import '../../../static/spine.dart';
import '../../../static/svg.dart';
import '../../learning_incentives/widget/alternative_image_widget.dart';
import '../controller.dart';
import '../enums/dynamic_action_type.dart';
import '../view_models/my_partners_view_models.dart';

/// 动态项 Widget
class DynamicItemWidget extends StatefulWidget {
  final DynamicViewModel dynamicModel;
  final MyPartnersCtrl controller;

  const DynamicItemWidget({
    Key? key,
    required this.dynamicModel,
    required this.controller,
  }) : super(key: key);

  @override
  State<DynamicItemWidget> createState() => _DynamicItemWidgetState();
}

class _DynamicItemWidgetState extends State<DynamicItemWidget>
    with TickerProviderStateMixin {
  final _defaultSubjectColor = "#FF9045";
  late JoJoSpineAnimationController _animationController;
  late AnimationController _shakeAnimationController;
  late Animation<double> _shakeAnimation;
  bool _isAnimationPlaying = false;
  bool _isShakeAnimationPlaying = false;

  @override
  void initState() {
    super.initState();
    _animationController = JoJoSpineAnimationController();

    // 初始化晃动动画控制器
    _shakeAnimationController = AnimationController(
      duration: const Duration(milliseconds: 900), // 总时长900ms
      vsync: this,
    );

    // 创建晃动动画序列
    final offset = 8.0.rdp;
    _shakeAnimation = TweenSequence<double>([
      TweenSequenceItem(
        tween: Tween<double>(begin: 0.0, end: -offset)
            .chain(CurveTween(curve: Curves.easeInOut)),
        weight: 100, // 100ms
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: -offset, end: offset)
            .chain(CurveTween(curve: Curves.easeInOut)),
        weight: 200, // 200ms
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: offset, end: -offset)
            .chain(CurveTween(curve: Curves.easeInOut)),
        weight: 200, // 200ms
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: -offset, end: offset)
            .chain(CurveTween(curve: Curves.easeInOut)),
        weight: 200, // 200ms
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: offset, end: 0.0)
            .chain(CurveTween(curve: Curves.easeInOut)),
        weight: 200, // 200ms
      ),
    ]).animate(_shakeAnimationController);

    _shakeAnimationController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        setState(() {
          _isShakeAnimationPlaying = false;
        });
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _shakeAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final route = widget.dynamicModel.userProfileUrl;
    var subjectColor = hexToColor(
                widget.dynamicModel.subjectColor ?? _defaultSubjectColor);
    return VisibilityObserve(
      onShow: () {
        RunEnv.sensorsTrack('\$AppViewScreen', {
          '\$screen_name': '学伴动态卡片曝光',
        });
      },
      child: GestureDetector(
        onTap: () {
          if (route?.isNotEmpty == true) {
            RunEnv.jumpLink(route ?? "");
          }
        },
        child: Container(
          margin: EdgeInsets.only(bottom: context.dimensions.mediumSpacing.rdp),
          padding: EdgeInsets.symmetric(
              vertical: context.dimensions.mediumSpacing.rdp, horizontal: 20.rdp),
          decoration: BoxDecoration(
            color: context.appColors.colorVariant1(subjectColor),
            borderRadius: BorderRadius.circular(10.rdp),
            border: Border.all(
                color: context.appColors.colorVariant2(subjectColor),
                width: 1.rdp),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                height: 20.rdp,
                child: _buildHeader(context),
              ),
              SizedBox(height: context.dimensions.smallSpacing.rdp),
              _buildContent(context),
              SizedBox(height: context.dimensions.smallSpacing.rdp),
              SizedBox(
                height: 40.rdp,
                child: _buildActionButton(
                    context, widget.dynamicModel.dynamicType ?? 0),
              )
            ],
          ),
        ),
      ),
    );
  }

  /// 构建头部（头像和用户信息）
  Widget _buildHeader(BuildContext context) {
    final color = context.appColors.colorVariant5(hexToColor(
            widget.dynamicModel.subjectColor ?? _defaultSubjectColor));
    final textStyle = context.textstyles.smallestText.pf.copyWith(
        color: color.withOpacity(0.7));
    return Row(
      children: [
        AnimatedBuilder(
          animation: _shakeAnimation,
          builder: (context, child) {
            return Transform.translate(
              offset: Offset(_shakeAnimation.value, 0),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  AlternativeImageWidget(
                    boxDecoration: const BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                    ),
                    imageUrl: widget.dynamicModel.userAvatar,
                    displayWidth: 20.rdp,
                    displayHeight: 20.rdp,
                    displayConfig: ImageDisplayConfig.head,
                    hideDefaultHolder: true,
                  ),
                  SizedBox(width: context.dimensions.minimumSpacing.rdp),
                  Text(
                    widget.dynamicModel.userName.length > 16
                        ? '${widget.dynamicModel.userName.substring(0, 16)}...'
                        : widget.dynamicModel.userName,
                    style: textStyle,
                  ),
                ],
              ),
            );
          },
        ),
        Expanded(
          child: Text(
            widget.dynamicModel.publishTime,
            textAlign: TextAlign.right,
            style: textStyle,
          ),
        ),
      ],
    );
  }

  /// 构建内容
  Widget _buildContent(BuildContext context) {
    final color = context.appColors.colorVariant6(
        hexToColor(widget.dynamicModel.subjectColor ?? _defaultSubjectColor)).withOpacity(0.7);
    final textStyle =
        context.textstyles.bodyTextEmphasis.pf.copyWith(color: color);
    return Text(
      widget.dynamicModel.dynamicContent,
      style: textStyle,
    );
  }

  /// 构建花花点击动画
  Widget _buildFlowerClickAnimation() {
    return SizedBox(
      height: 80.rdp,
      width: 80.rdp,
      child: JoJoSpineAnimationWidget(
        AssetsSpine.SPINE_FLOWER_CLIKC_ATKAS,
        AssetsSpine.SPINE_FLOWER_CLIKC_SKEL,
        LoadMode.assets,
        _animationController,
        package: Config.package,
        onInitialized: (controller) {
          if (mounted) {
            _animationController.playAnimation(JoJoSpineAnimation(
              animaitonName: "click",
              trackIndex: 0,
              loop: false,
              listener: (type) {
                if (type == AnimationEventType.complete) {
                  setState(() {
                    _isAnimationPlaying = false;
                  });
                }
              },
            ));
          }
        },
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButton(BuildContext context, int dynamicType) {
    // 根据 dynamicType 决定显示哪个按钮
    // 0: 不展示任何按钮
    // 1: 送花花按钮
    // 2: 戳一戳按钮
    final subjectColor =
        hexToColor(widget.dynamicModel.subjectColor ?? _defaultSubjectColor);

    final buttonPadding = EdgeInsets.symmetric(
        horizontal: context.dimensions.mediumSpacing.rdp,
        vertical: context.dimensions.smallSpacing.rdp);

    final iconSize = 16.rdp;
    final iconSpacing = SizedBox(width: context.dimensions.minimumSpacing.rdp);

    TextStyle getButtonTextStyle(bool isActive) {
      return context.textstyles.bodyText.pf.copyWith(
          color: isActive
              ? context.appColors.colorVariant6(subjectColor).withOpacity(0.5)
              : context.appColors.jColorGray6.withOpacity(0.7));
    }

    BoxDecoration getButtonDecoration(bool isActive) {
      return BoxDecoration(
        color: isActive
            ? context.appColors.colorVariant2(subjectColor)
            : Colors.white,
        borderRadius: BorderRadius.circular(
            context.dimensions.largeCornerRadius.rdp),
        border: Border.all(
          color: context.appColors.colorVariant2(subjectColor),
        ),
      );
    }
    switch (dynamicType) {
      case 1:
        bool isActive = widget.dynamicModel.actionState.hasGivenFlower;
        String buttonText =
            isActive ? S.of(context).alreadySend : S.of(context).sendFlower;
        return Container(
          clipBehavior: Clip.none,
          height: 40.rdp,
          child: Stack(
            clipBehavior: Clip.none,
            children: [
              GestureDetector(
                onTap: isActive || _isAnimationPlaying
                    ? null
                    : () {
                        setState(() {
                          _isAnimationPlaying = true;
                        });
                        l.d("MyPartnersCtrl", '_isAnimationPlaying: $_isAnimationPlaying');
                        widget.controller.sendAction(
                            DynamicActionType.flower,
                            widget.dynamicModel.dynamicId,
                            widget.dynamicModel.userId);

                        RunEnv.sensorsTrack('\$AppClick', {
                          '\$element_name': '学伴动态_赞一赞',
                        });
                      },
                child: Container(
                  padding: buttonPadding,
                  decoration: getButtonDecoration(isActive),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      SvgAssetWeb(
                        width: iconSize,
                        height: iconSize,
                        assetName: AssetsSvg.MY_PARTNERS_DYNAMIC_FLOWER,
                        fit: BoxFit.cover,
                        color: isActive
                            ? context.appColors.colorVariant6(subjectColor).withOpacity(0.3)
                            : context.appColors.jColorRed4,
                        package: Config.package,
                      ),
                      iconSpacing,
                      Text(
                        buttonText,
                        style: getButtonTextStyle(isActive),
                      )
                    ],
                  ),
                ),
              ),
              if (_isAnimationPlaying)
                Positioned(
                  top: -20.rdp,
                  left: -18.rdp,
                  child: _buildFlowerClickAnimation(),
                ),
            ],
          ),
        );
      case 2:
        bool isActive = widget.dynamicModel.actionState.hasPoked;
        String buttonText =
            isActive ? S.of(context).alreadyPoked : S.of(context).poke;
        return GestureDetector(
          onTap: isActive || _isShakeAnimationPlaying
              ? null
              : () {
                  setState(() {
                    _isShakeAnimationPlaying = true;
                  });
                  _shakeAnimationController.forward(from: 0);
                  widget.controller.sendAction(
                      DynamicActionType.poke,
                      widget.dynamicModel.dynamicId,
                      widget.dynamicModel.userId);
  
                  RunEnv.sensorsTrack('\$AppClick', {
                    '\$element_name': '学伴动态_戳一戳',
                  });
                },
          child: Container(
              padding: buttonPadding,
              decoration: getButtonDecoration(isActive),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  SvgAssetWeb(
                    width: iconSize,
                    height: iconSize,
                    assetName: AssetsSvg.MY_PARTNERS_DYNAMIC_POKE,
                    fit: BoxFit.cover,
                    color: isActive
                        ? context.appColors.colorVariant6(subjectColor).withOpacity(0.3)
                        : context.appColors.jColorPurple4,
                    package: Config.package,
                  ),
                  iconSpacing,
                  Text(
                    buttonText,
                    style: getButtonTextStyle(isActive),
                  ),
                ],
              )),
        );
      default:
        return const SizedBox.shrink(); // 不显示任何按钮
    }
  }
}
