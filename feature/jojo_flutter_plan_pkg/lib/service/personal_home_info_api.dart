import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/base_namespace.dart';
import 'package:jojo_flutter_base/config/address.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/model/course_map_home_page_tab_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/personal_home_module/model/growth_info_data.dart';
import '../common/dio/use.dart';
import '../pages/plan_home_map/personal_home_module/model/personal_info_data.dart';
import '../pages/plan_home_map/personal_home_module/model/subject_type_modules_info_data.dart';
part 'personal_home_info_api.g.dart';

@RestApi()
abstract class PersonalHomeApi {
  factory PersonalHomeApi(Dio dio, {String baseUrl}) = _PersonalHomeApi;

  /// 获取用户科目和课程
  @GET("/api/college/class-courses")
  Future<CourseSubjectTabData> getUserSubjectClass(
    @Query("partnerId") int? partnerId,
  );

  // 个人主页头部用户信息
  @GET("/api/pagani/incentive-baby-profiles")
  Future<PersonalInfo> requestPersonalHomeInfo(
    @Query("partnerId") int? partnerId,
    @Query("classKey") String? classKey,
  );

  // 获取科目下模块信息
  // 长曲线模块做了板块控制 2.1.0 版本号后才有数据,
  @GET("/api/pagani/subjects/{subjectType}/modules")
  Future<SubjectTypeModulesInfo> requestSubjectTypeModulesInfo(
    @Path('subjectType') int subjectType,
    @Query('partnerId') int? partnerId,
  );

  // 获取成长曲线时间
  /// @param classId 班级id
  @GET("/api/aurogon/classes/{classId}/schedule-views")
  Future<ScheduleViewsData> getScheduleViews(
    @Path('classId') int classId,
    @Query('viewType') int viewType,
    @Query('partnerId') int? partnerId,
  );

  /// 查询成长报告数据
  /// @param classId 班级id
  /// @param statisticKey 统计key 女娲获取
  /// @param statisticType 统计类型 女娲获取
  /// @param startTime 开始时间
  /// @param endTime 结束时间
  @GET("/api/aurogon/classes/{classId}/growth-statistic-indexes/{statisticKey}")
  Future<PeriodDataListData> getGrowthReportData(
    @Path('classId') int classId,
    @Path('statisticKey') String statisticKey,
    @Query('statisticType') int statisticType,
    @Query('startTime') int startTime,
    @Query('endTime') int endTime,
    @Query('partnerId') int? partnerId,
  );

  // 获取配置数据
  @GET("/api/nuwa/content/getSingleAdContent")
  Future<dynamic> getSingleAdContent(
    @Query('configKey') String configKey,
    @Header('TM-Application-identifying') String ciphertext,
    @Header('TM-UserAgent-deviceUniqueIdentifier')
        String deviceUniqueIdentifier,
  );

  //按钮点击回调
  @POST('/api/college/popup/callback')
  Future<dynamic> reportUpnew(@Body() Map<String, dynamic> map);
}

// final personalHomeApiService1 = PersonalHomeApi(pageDio,
//     baseUrl: 'https://mock.xjjj.co/mock/67735880100ff100115a055b/example');
final personalHomeApiService =
    PersonalHomeApi(pageDio, baseUrl: BaseAddress.baseCommonApiPath);
