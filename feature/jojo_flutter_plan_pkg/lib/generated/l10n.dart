// GENERATED CODE - DO NOT MODIFY BY HAND
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'intl/messages_all.dart';

// **************************************************************************
// Generator: Flutter Intl IDE plugin
// Made by Localizely
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values, avoid_escaping_inner_quotes

class S {
  S();

  static S? _current;

  static S get current {
    assert(_current != null,
        'No instance of S was loaded. Try to initialize the S delegate before accessing S.current.');
    return _current!;
  }

  static const AppLocalizationDelegate delegate = AppLocalizationDelegate();

  static Future<S> load(Locale locale) {
    final name = (locale.countryCode?.isEmpty ?? false)
        ? locale.languageCode
        : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = S();
      S._current = instance;

      return instance;
    });
  }

  static S of(BuildContext context) {
    final instance = S.maybeOf(context);
    assert(instance != null,
        'No instance of S present in the widget tree. Did you add S.delegate in localizationsDelegates?');
    return instance!;
  }

  static S? maybeOf(BuildContext context) {
    return Localizations.of<S>(context, S);
  }

  /// `已复制文本，粘贴到朋友圈让大家为你点赞吧`
  String get shareCopyTips {
    return Intl.message(
      '已复制文本，粘贴到朋友圈让大家为你点赞吧',
      name: 'shareCopyTips',
      desc: '',
      args: [],
    );
  }

  /// `全部计划`
  String get all_courses {
    return Intl.message(
      '全部计划',
      name: 'all_courses',
      desc: '',
      args: [],
    );
  }

  /// `计划详情`
  String get course_detail {
    return Intl.message(
      '计划详情',
      name: 'course_detail',
      desc: '',
      args: [],
    );
  }

  /// `我的成就`
  String get myAchievements {
    return Intl.message(
      '我的成就',
      name: 'myAchievements',
      desc: '',
      args: [],
    );
  }

  /// `成就详情`
  String get achievement_detail {
    return Intl.message(
      '成就详情',
      name: 'achievement_detail',
      desc: '',
      args: [],
    );
  }

  /// `暂未获得成就哦`
  String get noAchievementsYet {
    return Intl.message(
      '暂未获得成就哦',
      name: 'noAchievementsYet',
      desc: '',
      args: [],
    );
  }

  /// `成就详情`
  String get achievementsDetail {
    return Intl.message(
      '成就详情',
      name: 'achievementsDetail',
      desc: '',
      args: [],
    );
  }

  /// `暂无该类型计划`
  String get noPlansAvailable {
    return Intl.message(
      '暂无该类型计划',
      name: 'noPlansAvailable',
      desc: '',
      args: [],
    );
  }

  /// `天`
  String get consecutiveUnit {
    return Intl.message(
      '天',
      name: 'consecutiveUnit',
      desc: '',
      args: [],
    );
  }

  /// `连续坚持`
  String get consecutiveTitle {
    return Intl.message(
      '连续坚持',
      name: 'consecutiveTitle',
      desc: '',
      args: [],
    );
  }

  /// `历史最佳`
  String get bestHistory {
    return Intl.message(
      '历史最佳',
      name: 'bestHistory',
      desc: '',
      args: [],
    );
  }

  /// `待开始`
  String get waitStart {
    return Intl.message(
      '待开始',
      name: 'waitStart',
      desc: '',
      args: [],
    );
  }

  /// `学习之旅就像张空白画布将充满无限可能`
  String get pureNewUserPageMessage {
    return Intl.message(
      '学习之旅就像张空白画布将充满无限可能',
      name: 'pureNewUserPageMessage',
      desc: '',
      args: [],
    );
  }

  /// `暂无数据`
  String get emptyData {
    return Intl.message(
      '暂无数据',
      name: 'emptyData',
      desc: '',
      args: [],
    );
  }

  /// `我知道了`
  String get iKnow {
    return Intl.message(
      '我知道了',
      name: 'iKnow',
      desc: '',
      args: [],
    );
  }

  /// `接受任务`
  String get aceptTask {
    return Intl.message(
      '接受任务',
      name: 'aceptTask',
      desc: '',
      args: [],
    );
  }

  /// `参数异常`
  String get parameterException {
    return Intl.message(
      '参数异常',
      name: 'parameterException',
      desc: '',
      args: [],
    );
  }

  /// `开始时间`
  String get planLessonClassTime {
    return Intl.message(
      '开始时间',
      name: 'planLessonClassTime',
      desc: '',
      args: [],
    );
  }

  /// `今`
  String get planLessonTodayTag {
    return Intl.message(
      '今',
      name: 'planLessonTodayTag',
      desc: '',
      args: [],
    );
  }

  /// `补`
  String get planLessonSupplementTag {
    return Intl.message(
      '补',
      name: 'planLessonSupplementTag',
      desc: '',
      args: [],
    );
  }

  /// `连胜道具`
  String get consecutiveWinTool {
    return Intl.message(
      '连胜道具',
      name: 'consecutiveWinTool',
      desc: '',
      args: [],
    );
  }

  /// `我的连胜`
  String get myConsecutiveWin {
    return Intl.message(
      '我的连胜',
      name: 'myConsecutiveWin',
      desc: '',
      args: [],
    );
  }

  /// `复活道具`
  String get reviveTool {
    return Intl.message(
      '复活道具',
      name: 'reviveTool',
      desc: '',
      args: [],
    );
  }

  /// `购买并使用`
  String get buyAndUseProp {
    return Intl.message(
      '购买并使用',
      name: 'buyAndUseProp',
      desc: '',
      args: [],
    );
  }

  /// `购买失败`
  String get buyFailed {
    return Intl.message(
      '购买失败',
      name: 'buyFailed',
      desc: '',
      args: [],
    );
  }

  /// `确认使用`
  String get sureUse {
    return Intl.message(
      '确认使用',
      name: 'sureUse',
      desc: '',
      args: [],
    );
  }

  /// `购买并使用当前道具？`
  String get buyAndUseCurrentProp {
    return Intl.message(
      '购买并使用当前道具？',
      name: 'buyAndUseCurrentProp',
      desc: '',
      args: [],
    );
  }

  /// `道具购买暂无响应，`
  String get propBuyTimeout {
    return Intl.message(
      '道具购买暂无响应，',
      name: 'propBuyTimeout',
      desc: '',
      args: [],
    );
  }

  /// `请问需要继续等待吗？`
  String get needToWait {
    return Intl.message(
      '请问需要继续等待吗？',
      name: 'needToWait',
      desc: '',
      args: [],
    );
  }

  /// `道具购买失败，请返回`
  String get buyFailedAndBack {
    return Intl.message(
      '道具购买失败，请返回',
      name: 'buyFailedAndBack',
      desc: '',
      args: [],
    );
  }

  /// `请稍候`
  String get pleaseWait {
    return Intl.message(
      '请稍候',
      name: 'pleaseWait',
      desc: '',
      args: [],
    );
  }

  /// `去连接`
  String get toConsecutiveWin {
    return Intl.message(
      '去连接',
      name: 'toConsecutiveWin',
      desc: '',
      args: [],
    );
  }

  /// `一`
  String get one {
    return Intl.message(
      '一',
      name: 'one',
      desc: '',
      args: [],
    );
  }

  /// `二`
  String get two {
    return Intl.message(
      '二',
      name: 'two',
      desc: '',
      args: [],
    );
  }

  /// `三`
  String get three {
    return Intl.message(
      '三',
      name: 'three',
      desc: '',
      args: [],
    );
  }

  /// `四`
  String get four {
    return Intl.message(
      '四',
      name: 'four',
      desc: '',
      args: [],
    );
  }

  /// `五`
  String get five {
    return Intl.message(
      '五',
      name: 'five',
      desc: '',
      args: [],
    );
  }

  /// `六`
  String get six {
    return Intl.message(
      '六',
      name: 'six',
      desc: '',
      args: [],
    );
  }

  /// `日`
  String get day {
    return Intl.message(
      '日',
      name: 'day',
      desc: '',
      args: [],
    );
  }

  /// `连胜日历`
  String get consecutiveWinCalendar {
    return Intl.message(
      '连胜日历',
      name: 'consecutiveWinCalendar',
      desc: '',
      args: [],
    );
  }

  /// `连胜`
  String get consecutiveWin {
    return Intl.message(
      '连胜',
      name: 'consecutiveWin',
      desc: '',
      args: [],
    );
  }

  /// `年`
  String get year {
    return Intl.message(
      '年',
      name: 'year',
      desc: '',
      args: [],
    );
  }

  /// `月`
  String get month {
    return Intl.message(
      '月',
      name: 'month',
      desc: '',
      args: [],
    );
  }

  /// `去学习`
  String get goStudy {
    return Intl.message(
      '去学习',
      name: 'goStudy',
      desc: '',
      args: [],
    );
  }

  /// `次`
  String get classTxt {
    return Intl.message(
      '次',
      name: 'classTxt',
      desc: '',
      args: [],
    );
  }

  /// `第`
  String get diTxt {
    return Intl.message(
      '第',
      name: 'diTxt',
      desc: '',
      args: [],
    );
  }

  /// `叫叫`
  String get JoJo {
    return Intl.message(
      '叫叫',
      name: 'JoJo',
      desc: '',
      args: [],
    );
  }

  /// `绿豆`
  String get greenPulse {
    return Intl.message(
      '绿豆',
      name: 'greenPulse',
      desc: '',
      args: [],
    );
  }

  /// `点亮`
  String get goToLight {
    return Intl.message(
      '点亮',
      name: 'goToLight',
      desc: '',
      args: [],
    );
  }

  /// `已点亮`
  String get alreadyLight {
    return Intl.message(
      '已点亮',
      name: 'alreadyLight',
      desc: '',
      args: [],
    );
  }

  /// `连结连续学天数`
  String get consecutiveWinToolMessage {
    return Intl.message(
      '连结连续学天数',
      name: 'consecutiveWinToolMessage',
      desc: '',
      args: [],
    );
  }

  /// `连接连胜`
  String get consecutiveWinMessage {
    return Intl.message(
      '连接连胜',
      name: 'consecutiveWinMessage',
      desc: '',
      args: [],
    );
  }

  /// `回顾`
  String get review {
    return Intl.message(
      '回顾',
      name: 'review',
      desc: '',
      args: [],
    );
  }

  /// `再学一次`
  String get learnAgain {
    return Intl.message(
      '再学一次',
      name: 'learnAgain',
      desc: '',
      args: [],
    );
  }

  /// `补学拯救`
  String get repairStudy {
    return Intl.message(
      '补学拯救',
      name: 'repairStudy',
      desc: '',
      args: [],
    );
  }

  /// `去拯救`
  String get toRepirStudy {
    return Intl.message(
      '去拯救',
      name: 'toRepirStudy',
      desc: '',
      args: [],
    );
  }

  /// `复活`
  String get revive {
    return Intl.message(
      '复活',
      name: 'revive',
      desc: '',
      args: [],
    );
  }

  /// `去复活`
  String get toRevive {
    return Intl.message(
      '去复活',
      name: 'toRevive',
      desc: '',
      args: [],
    );
  }

  /// `复活并拯救叫叫`
  String get reviveRepairJOJO {
    return Intl.message(
      '复活并拯救叫叫',
      name: 'reviveRepairJOJO',
      desc: '',
      args: [],
    );
  }

  /// `道具使用失败，请稍后再试`
  String get toolUseFailMsg {
    return Intl.message(
      '道具使用失败，请稍后再试',
      name: 'toolUseFailMsg',
      desc: '',
      args: [],
    );
  }

  /// `学习预告`
  String get planLessonSubscribe {
    return Intl.message(
      '学习预告',
      name: 'planLessonSubscribe',
      desc: '',
      args: [],
    );
  }

  /// `自动续订`
  String get planLessonAutoSubscribe {
    return Intl.message(
      '自动续订',
      name: 'planLessonAutoSubscribe',
      desc: '',
      args: [],
    );
  }

  /// `学后服务`
  String get afterClassSevice {
    return Intl.message(
      '学后服务',
      name: 'afterClassSevice',
      desc: '',
      args: [],
    );
  }

  /// `思维计划表`
  String get mathScheduleList {
    return Intl.message(
      '思维计划表',
      name: 'mathScheduleList',
      desc: '',
      args: [],
    );
  }

  /// `未解锁`
  String get lock {
    return Intl.message(
      '未解锁',
      name: 'lock',
      desc: '',
      args: [],
    );
  }

  /// `全部`
  String get all {
    return Intl.message(
      '全部',
      name: 'all',
      desc: '',
      args: [],
    );
  }

  /// `待补`
  String get pendingMakeupCourses_abbr {
    return Intl.message(
      '待补',
      name: 'pendingMakeupCourses_abbr',
      desc: '',
      args: [],
    );
  }

  /// `当前没有内容哦～`
  String get emptyCoursesTip {
    return Intl.message(
      '当前没有内容哦～',
      name: 'emptyCoursesTip',
      desc: '',
      args: [],
    );
  }

  /// `这个阶段没有要补读的内容哦！`
  String get emptyPendingMakeupCoursesTip {
    return Intl.message(
      '这个阶段没有要补读的内容哦！',
      name: 'emptyPendingMakeupCoursesTip',
      desc: '',
      args: [],
    );
  }

  /// `取消`
  String get cancel {
    return Intl.message(
      '取消',
      name: 'cancel',
      desc: '',
      args: [],
    );
  }

  /// `我再想想`
  String get thinkAgain {
    return Intl.message(
      '我再想想',
      name: 'thinkAgain',
      desc: '',
      args: [],
    );
  }

  /// `确定`
  String get confirm {
    return Intl.message(
      '确定',
      name: 'confirm',
      desc: '',
      args: [],
    );
  }

  /// `完成情况`
  String get completionStatus {
    return Intl.message(
      '完成情况',
      name: 'completionStatus',
      desc: '',
      args: [],
    );
  }

  /// `查看往期计划服务`
  String get viewPastCourses {
    return Intl.message(
      '查看往期计划服务',
      name: 'viewPastCourses',
      desc: '',
      args: [],
    );
  }

  /// `收起往期计划服务`
  String get hidePastCourses {
    return Intl.message(
      '收起往期计划服务',
      name: 'hidePastCourses',
      desc: '',
      args: [],
    );
  }

  /// `学后服务`
  String get afterClass {
    return Intl.message(
      '学后服务',
      name: 'afterClass',
      desc: '',
      args: [],
    );
  }

  /// `换装`
  String get editDress {
    return Intl.message(
      '换装',
      name: 'editDress',
      desc: '',
      args: [],
    );
  }

  /// `已加入叫叫`
  String get learnedInJOJO {
    return Intl.message(
      '已加入叫叫',
      name: 'learnedInJOJO',
      desc: '',
      args: [],
    );
  }

  /// `狠心忽略`
  String get forceIgnore {
    return Intl.message(
      '狠心忽略',
      name: 'forceIgnore',
      desc: '',
      args: [],
    );
  }

  /// `剩余`
  String get remain {
    return Intl.message(
      '剩余',
      name: 'remain',
      desc: '',
      args: [],
    );
  }

  /// `个`
  String get num {
    return Intl.message(
      '个',
      name: 'num',
      desc: '',
      args: [],
    );
  }

  /// `收下`
  String get receive {
    return Intl.message(
      '收下',
      name: 'receive',
      desc: '',
      args: [],
    );
  }

  /// `下一步`
  String get nextStep {
    return Intl.message(
      '下一步',
      name: 'nextStep',
      desc: '',
      args: [],
    );
  }

  /// `连续坚持`
  String get continuousPersistence {
    return Intl.message(
      '连续坚持',
      name: 'continuousPersistence',
      desc: '',
      args: [],
    );
  }

  /// `补学救援`
  String get relearnForAssistance {
    return Intl.message(
      '补学救援',
      name: 'relearnForAssistance',
      desc: '',
      args: [],
    );
  }

  /// `被你的学习热情融化了！`
  String get frozenBigTip {
    return Intl.message(
      '被你的学习热情融化了！',
      name: 'frozenBigTip',
      desc: '',
      args: [],
    );
  }

  /// `点燃你的学习热情，现在就去营救~！`
  String get forzenFinalBigTip {
    return Intl.message(
      '点燃你的学习热情，现在就去营救~！',
      name: 'forzenFinalBigTip',
      desc: '',
      args: [],
    );
  }

  /// `就差一步，使用道具复活吧！`
  String get dieBigTip {
    return Intl.message(
      '就差一步，使用道具复活吧！',
      name: 'dieBigTip',
      desc: '',
      args: [],
    );
  }

  /// `我一定来`
  String get iWillCome {
    return Intl.message(
      '我一定来',
      name: 'iWillCome',
      desc: '',
      args: [],
    );
  }

  /// `待开始`
  String get waitClassStart {
    return Intl.message(
      '待开始',
      name: 'waitClassStart',
      desc: '',
      args: [],
    );
  }

  /// `开始`
  String get classStart {
    return Intl.message(
      '开始',
      name: 'classStart',
      desc: '',
      args: [],
    );
  }

  /// `计划`
  String get lesson {
    return Intl.message(
      '计划',
      name: 'lesson',
      desc: '',
      args: [],
    );
  }

  /// `开始`
  String get start {
    return Intl.message(
      '开始',
      name: 'start',
      desc: '',
      args: [],
    );
  }

  /// `记得按时来学习哦～`
  String get rememberGoStudyOnTime {
    return Intl.message(
      '记得按时来学习哦～',
      name: 'rememberGoStudyOnTime',
      desc: '',
      args: [],
    );
  }

  /// `继续保持`
  String get continueMaintain {
    return Intl.message(
      '继续保持',
      name: 'continueMaintain',
      desc: '',
      args: [],
    );
  }

  /// `道具使用失败`
  String get useToolFailTip {
    return Intl.message(
      '道具使用失败',
      name: 'useToolFailTip',
      desc: '',
      args: [],
    );
  }

  /// `今天没有计划哦~`
  String get lessonIsEmpty {
    return Intl.message(
      '今天没有计划哦~',
      name: 'lessonIsEmpty',
      desc: '',
      args: [],
    );
  }

  /// `规则说明`
  String get ruleDesc {
    return Intl.message(
      '规则说明',
      name: 'ruleDesc',
      desc: '',
      args: [],
    );
  }

  /// `赠送内容`
  String get courseGift {
    return Intl.message(
      '赠送内容',
      name: 'courseGift',
      desc: '',
      args: [],
    );
  }

  /// `精彩正在准备中~`
  String get loadingReadyTip {
    return Intl.message(
      '精彩正在准备中~',
      name: 'loadingReadyTip',
      desc: '',
      args: [],
    );
  }

  /// `太棒了！已经是最高等级！`
  String get highestMedalMessage {
    return Intl.message(
      '太棒了！已经是最高等级！',
      name: 'highestMedalMessage',
      desc: '',
      args: [],
    );
  }

  /// `奖章介绍`
  String get medalDescription {
    return Intl.message(
      '奖章介绍',
      name: 'medalDescription',
      desc: '',
      args: [],
    );
  }

  /// `当前没有进行中的计划服务哦~`
  String get learningLessonIsEmpty {
    return Intl.message(
      '当前没有进行中的计划服务哦~',
      name: 'learningLessonIsEmpty',
      desc: '',
      args: [],
    );
  }

  /// `宝石`
  String get gemstone {
    return Intl.message(
      '宝石',
      name: 'gemstone',
      desc: '',
      args: [],
    );
  }

  /// `成长豆`
  String get beans {
    return Intl.message(
      '成长豆',
      name: 'beans',
      desc: '',
      args: [],
    );
  }

  /// `资产`
  String get balance {
    return Intl.message(
      '资产',
      name: 'balance',
      desc: '',
      args: [],
    );
  }

  /// `道具功能`
  String get functionDes {
    return Intl.message(
      '道具功能',
      name: 'functionDes',
      desc: '',
      args: [],
    );
  }

  /// `当前剩余`
  String get ownedCount {
    return Intl.message(
      '当前剩余',
      name: 'ownedCount',
      desc: '',
      args: [],
    );
  }

  /// `获取途径`
  String get getFuncDes {
    return Intl.message(
      '获取途径',
      name: 'getFuncDes',
      desc: '',
      args: [],
    );
  }

  /// `太棒了`
  String get soGood {
    return Intl.message(
      '太棒了',
      name: 'soGood',
      desc: '',
      args: [],
    );
  }

  /// `恭喜获得物品！`
  String get congratulationsGetGoods {
    return Intl.message(
      '恭喜获得物品！',
      name: 'congratulationsGetGoods',
      desc: '',
      args: [],
    );
  }

  /// `系统繁忙，请稍后再试`
  String get systemError {
    return Intl.message(
      '系统繁忙，请稍后再试',
      name: 'systemError',
      desc: '',
      args: [],
    );
  }

  /// `超级赞！\n点亮一个属于你的学习火焰`
  String get continueNormalDayNotChangeTip {
    return Intl.message(
      '超级赞！\n点亮一个属于你的学习火焰',
      name: 'continueNormalDayNotChangeTip',
      desc: '',
      args: [],
    );
  }

  /// `连续坚持中断啦......`
  String get continueIceDayNotChangeTip {
    return Intl.message(
      '连续坚持中断啦......',
      name: 'continueIceDayNotChangeTip',
      desc: '',
      args: [],
    );
  }

  /// `什么！今天还没学习？`
  String get continueTodayNoFinishTip {
    return Intl.message(
      '什么！今天还没学习？',
      name: 'continueTodayNoFinishTip',
      desc: '',
      args: [],
    );
  }

  /// `连胜冲刺开启`
  String get continuousSprintOpen {
    return Intl.message(
      '连胜冲刺开启',
      name: 'continuousSprintOpen',
      desc: '',
      args: [],
    );
  }

  /// `好的`
  String get ok {
    return Intl.message(
      '好的',
      name: 'ok',
      desc: '',
      args: [],
    );
  }

  /// `获得一个宝箱！`
  String get getOneBox {
    return Intl.message(
      '获得一个宝箱！',
      name: 'getOneBox',
      desc: '',
      args: [],
    );
  }

  /// `里程碑奖励`
  String get milestoneAward {
    return Intl.message(
      '里程碑奖励',
      name: 'milestoneAward',
      desc: '',
      args: [],
    );
  }

  /// `暂未获得里程碑奖励哦`
  String get milestoneEmpty {
    return Intl.message(
      '暂未获得里程碑奖励哦',
      name: 'milestoneEmpty',
      desc: '',
      args: [],
    );
  }

  /// `学习搭子`
  String get studyPartner {
    return Intl.message(
      '学习搭子',
      name: 'studyPartner',
      desc: '',
      args: [],
    );
  }

  /// `更多`
  String get more {
    return Intl.message(
      '更多',
      name: 'more',
      desc: '',
      args: [],
    );
  }

  /// `在叫叫学习了{days}天`
  String stduyAllCount(Object days) {
    return Intl.message(
      '在叫叫学习了$days天',
      name: 'stduyAllCount',
      desc: '',
      args: [days],
    );
  }

  /// `成就`
  String get achievement {
    return Intl.message(
      '成就',
      name: 'achievement',
      desc: '',
      args: [],
    );
  }

  /// `点击打开`
  String get clickAndOpen {
    return Intl.message(
      '点击打开',
      name: 'clickAndOpen',
      desc: '',
      args: [],
    );
  }

  /// `点击继续`
  String get clickAndContinue {
    return Intl.message(
      '点击继续',
      name: 'clickAndContinue',
      desc: '',
      args: [],
    );
  }

  /// `开启护眼模式`
  String get eyeProtectionTitle {
    return Intl.message(
      '开启护眼模式',
      name: 'eyeProtectionTitle',
      desc: '',
      args: [],
    );
  }

  /// `看视频的同时注意保护视力哦~开启护眼模式，叫叫陪你养成良好用眼习惯。`
  String get eyeProtectionDesc {
    return Intl.message(
      '看视频的同时注意保护视力哦~开启护眼模式，叫叫陪你养成良好用眼习惯。',
      name: 'eyeProtectionDesc',
      desc: '',
      args: [],
    );
  }

  /// `可在“我的-设置”中开启/关闭护眼模式`
  String get eyeProtectionSettingTip {
    return Intl.message(
      '可在“我的-设置”中开启/关闭护眼模式',
      name: 'eyeProtectionSettingTip',
      desc: '',
      args: [],
    );
  }

  /// `查看护眼原理`
  String get eyeProtectionPrinciple {
    return Intl.message(
      '查看护眼原理',
      name: 'eyeProtectionPrinciple',
      desc: '',
      args: [],
    );
  }

  /// `下次再说`
  String get eyeProtectionNextTime {
    return Intl.message(
      '下次再说',
      name: 'eyeProtectionNextTime',
      desc: '',
      args: [],
    );
  }

  /// `开启`
  String get eyeProtectionOpen {
    return Intl.message(
      '开启',
      name: 'eyeProtectionOpen',
      desc: '',
      args: [],
    );
  }

  /// `一键完成`
  String get completeAllCourses {
    return Intl.message(
      '一键完成',
      name: 'completeAllCourses',
      desc: '',
      args: [],
    );
  }

  /// `审`
  String get reviser {
    return Intl.message(
      '审',
      name: 'reviser',
      desc: '',
      args: [],
    );
  }

  /// `多题审校`
  String get multipleRevise {
    return Intl.message(
      '多题审校',
      name: 'multipleRevise',
      desc: '',
      args: [],
    );
  }

  /// `基础知识审校`
  String get basicKnowledgeRevise {
    return Intl.message(
      '基础知识审校',
      name: 'basicKnowledgeRevise',
      desc: '',
      args: [],
    );
  }

  /// `我一定行`
  String get iamGood {
    return Intl.message(
      '我一定行',
      name: 'iamGood',
      desc: '',
      args: [],
    );
  }

  /// `了解一下`
  String get knowMore {
    return Intl.message(
      '了解一下',
      name: 'knowMore',
      desc: '',
      args: [],
    );
  }

  /// `您的道具不足，现在就去获得道具吧`
  String get lackTool {
    return Intl.message(
      '您的道具不足，现在就去获得道具吧',
      name: 'lackTool',
      desc: '',
      args: [],
    );
  }

  /// `分享成就`
  String get shareMyAchievement {
    return Intl.message(
      '分享成就',
      name: 'shareMyAchievement',
      desc: '',
      args: [],
    );
  }

  /// `直接收下`
  String get takeIt {
    return Intl.message(
      '直接收下',
      name: 'takeIt',
      desc: '',
      args: [],
    );
  }

  /// `剩余{days}天{hours}小时`
  String remainingDaysHours(Object days, Object hours) {
    return Intl.message(
      '剩余$days天$hours小时',
      name: 'remainingDaysHours',
      desc: '',
      args: [days, hours],
    );
  }

  /// `剩余{hours}:{minutes}`
  String remainingHoursMinutes(Object hours, Object minutes) {
    return Intl.message(
      '剩余$hours:$minutes',
      name: 'remainingHoursMinutes',
      desc: '',
      args: [hours, minutes],
    );
  }

  /// `补学助手`
  String get reviewAssistant {
    return Intl.message(
      '补学助手',
      name: 'reviewAssistant',
      desc: '',
      args: [],
    );
  }

  /// `请选择主题`
  String get pleaseChooseThemeMonth {
    return Intl.message(
      '请选择主题',
      name: 'pleaseChooseThemeMonth',
      desc: '',
      args: [],
    );
  }

  /// `下拉切换到上个主题`
  String get changeThemeMonthUp {
    return Intl.message(
      '下拉切换到上个主题',
      name: 'changeThemeMonthUp',
      desc: '',
      args: [],
    );
  }

  /// `上拉切换到下个主题`
  String get changeThemeMonthDown {
    return Intl.message(
      '上拉切换到下个主题',
      name: 'changeThemeMonthDown',
      desc: '',
      args: [],
    );
  }

  /// `这个阶段没有要补读的内容哦～`
  String get emptyReviewLessonData {
    return Intl.message(
      '这个阶段没有要补读的内容哦～',
      name: 'emptyReviewLessonData',
      desc: '',
      args: [],
    );
  }

  /// `这个阶段没有课程内容哦～`
  String get emptyAllLessonData {
    return Intl.message(
      '这个阶段没有课程内容哦～',
      name: 'emptyAllLessonData',
      desc: '',
      args: [],
    );
  }

  /// `内容获取失败，请重试...`
  String get reviewPageRequestError {
    return Intl.message(
      '内容获取失败，请重试...',
      name: 'reviewPageRequestError',
      desc: '',
      args: [],
    );
  }

  /// `服务点评`
  String get serviceComment {
    return Intl.message(
      '服务点评',
      name: 'serviceComment',
      desc: '',
      args: [],
    );
  }

  /// `暂无服务点评`
  String get serviceEmptyComment {
    return Intl.message(
      '暂无服务点评',
      name: 'serviceEmptyComment',
      desc: '',
      args: [],
    );
  }

  /// `提交作品后，班班会进行点评`
  String get serviceCommentTips {
    return Intl.message(
      '提交作品后，班班会进行点评',
      name: 'serviceCommentTips',
      desc: '',
      args: [],
    );
  }

  /// `班班服务`
  String get serviceGuide {
    return Intl.message(
      '班班服务',
      name: 'serviceGuide',
      desc: '',
      args: [],
    );
  }

  /// `班班`
  String get serviceSuffix {
    return Intl.message(
      '班班',
      name: 'serviceSuffix',
      desc: '',
      args: [],
    );
  }

  /// `分享我的高光时刻`
  String get shareMyHighlightMoment {
    return Intl.message(
      '分享我的高光时刻',
      name: 'shareMyHighlightMoment',
      desc: '',
      args: [],
    );
  }

  /// `微信好友`
  String get wxFriend {
    return Intl.message(
      '微信好友',
      name: 'wxFriend',
      desc: '',
      args: [],
    );
  }

  /// `朋友圈`
  String get wxCircle {
    return Intl.message(
      '朋友圈',
      name: 'wxCircle',
      desc: '',
      args: [],
    );
  }

  /// `QQ`
  String get qq {
    return Intl.message(
      'QQ',
      name: 'qq',
      desc: '',
      args: [],
    );
  }

  /// `保存相册`
  String get saveAlbum {
    return Intl.message(
      '保存相册',
      name: 'saveAlbum',
      desc: '',
      args: [],
    );
  }

  /// `图片生成中...`
  String get pictureGenerating {
    return Intl.message(
      '图片生成中...',
      name: 'pictureGenerating',
      desc: '',
      args: [],
    );
  }

  /// `保存成功`
  String get saveSuccess {
    return Intl.message(
      '保存成功',
      name: 'saveSuccess',
      desc: '',
      args: [],
    );
  }

  /// `保存失败`
  String get saveFail {
    return Intl.message(
      '保存失败',
      name: 'saveFail',
      desc: '',
      args: [],
    );
  }

  /// `分享失败`
  String get shareFail {
    return Intl.message(
      '分享失败',
      name: 'shareFail',
      desc: '',
      args: [],
    );
  }

  /// `我在`
  String get shareDayCountFirstTip {
    return Intl.message(
      '我在',
      name: 'shareDayCountFirstTip',
      desc: '',
      args: [],
    );
  }

  /// `连续学习了`
  String get shareDayCountSecondTip {
    return Intl.message(
      '连续学习了',
      name: 'shareDayCountSecondTip',
      desc: '',
      args: [],
    );
  }

  /// `你想成为我的学习搭子吗？`
  String get qrcodeTip {
    return Intl.message(
      '你想成为我的学习搭子吗？',
      name: 'qrcodeTip',
      desc: '',
      args: [],
    );
  }

  /// `学前准备`
  String get learningReady {
    return Intl.message(
      '学前准备',
      name: 'learningReady',
      desc: '',
      args: [],
    );
  }

  /// `{unFinishCount}个未完成`
  String learningReadyUnFinish(Object unFinishCount) {
    return Intl.message(
      '$unFinishCount个未完成',
      name: 'learningReadyUnFinish',
      desc: '',
      args: [unFinishCount],
    );
  }

  /// `展开`
  String get expand {
    return Intl.message(
      '展开',
      name: 'expand',
      desc: '',
      args: [],
    );
  }

  /// `收起`
  String get fold {
    return Intl.message(
      '收起',
      name: 'fold',
      desc: '',
      args: [],
    );
  }

  /// `最近搜索`
  String get recentSearch {
    return Intl.message(
      '最近搜索',
      name: 'recentSearch',
      desc: '',
      args: [],
    );
  }

  /// `搜索`
  String get search {
    return Intl.message(
      '搜索',
      name: 'search',
      desc: '',
      args: [],
    );
  }

  /// `没有查找到内容哦`
  String get noSearchData {
    return Intl.message(
      '没有查找到内容哦',
      name: 'noSearchData',
      desc: '',
      args: [],
    );
  }

  /// `缺少搜索关键字`
  String get searchKeywordEmpty {
    return Intl.message(
      '缺少搜索关键字',
      name: 'searchKeywordEmpty',
      desc: '',
      args: [],
    );
  }

  /// `请输入计划名称或编号`
  String get searchHintText {
    return Intl.message(
      '请输入计划名称或编号',
      name: 'searchHintText',
      desc: '',
      args: [],
    );
  }

  /// `获取报告解读`
  String get trainAddTeacherPageTitle {
    return Intl.message(
      '获取报告解读',
      name: 'trainAddTeacherPageTitle',
      desc: '',
      args: [],
    );
  }

  /// `当前主题还没有解锁哦`
  String get currentStageUnlock {
    return Intl.message(
      '当前主题还没有解锁哦',
      name: 'currentStageUnlock',
      desc: '',
      args: [],
    );
  }

  /// `全部内容`
  String get lessonAll {
    return Intl.message(
      '全部内容',
      name: 'lessonAll',
      desc: '',
      args: [],
    );
  }

  /// `权益生效中`
  String get lessonInteresting {
    return Intl.message(
      '权益生效中',
      name: 'lessonInteresting',
      desc: '',
      args: [],
    );
  }

  /// `更换年级`
  String get lessonInterested {
    return Intl.message(
      '更换年级',
      name: 'lessonInterested',
      desc: '',
      args: [],
    );
  }

  /// `更换`
  String get lessonChange {
    return Intl.message(
      '更换',
      name: 'lessonChange',
      desc: '',
      args: [],
    );
  }

  /// `取消`
  String get lessonChangeCancel {
    return Intl.message(
      '取消',
      name: 'lessonChangeCancel',
      desc: '',
      args: [],
    );
  }

  /// `重试`
  String get lessonChangeRetry {
    return Intl.message(
      '重试',
      name: 'lessonChangeRetry',
      desc: '',
      args: [],
    );
  }

  /// `您已更换过一次年级，无法再次更换`
  String get lessonUnChange {
    return Intl.message(
      '您已更换过一次年级，无法再次更换',
      name: 'lessonUnChange',
      desc: '',
      args: [],
    );
  }

  /// `仅可更换一次，请谨慎操作\n更换前：{beforeSkuName}\n更换后：{changeSkuName}`
  String lessonChangeDes(Object beforeSkuName, Object changeSkuName) {
    return Intl.message(
      '仅可更换一次，请谨慎操作\n更换前：$beforeSkuName\n更换后：$changeSkuName',
      name: 'lessonChangeDes',
      desc: '',
      args: [beforeSkuName, changeSkuName],
    );
  }

  /// `更换中…`
  String get lessonLoadingText {
    return Intl.message(
      '更换中…',
      name: 'lessonLoadingText',
      desc: '',
      args: [],
    );
  }

  /// `年级更换成功`
  String get lessonChangeSuccess {
    return Intl.message(
      '年级更换成功',
      name: 'lessonChangeSuccess',
      desc: '',
      args: [],
    );
  }

  /// `年级更换失败`
  String get lessonChangeFail {
    return Intl.message(
      '年级更换失败',
      name: 'lessonChangeFail',
      desc: '',
      args: [],
    );
  }

  /// `请稍后重试，或`
  String get lessonChangeFailRetry {
    return Intl.message(
      '请稍后重试，或',
      name: 'lessonChangeFailRetry',
      desc: '',
      args: [],
    );
  }

  /// `联系客服`
  String get lessonChangeFailContact {
    return Intl.message(
      '联系客服',
      name: 'lessonChangeFailContact',
      desc: '',
      args: [],
    );
  }

  /// `处理`
  String get lessonChangeFailDeal {
    return Intl.message(
      '处理',
      name: 'lessonChangeFailDeal',
      desc: '',
      args: [],
    );
  }

  /// `激活失败`
  String get lessonChangeActionFail {
    return Intl.message(
      '激活失败',
      name: 'lessonChangeActionFail',
      desc: '',
      args: [],
    );
  }

  /// `新年级激活失败，请联系客服处理!`
  String get lessonChangeActionFailDetail {
    return Intl.message(
      '新年级激活失败，请联系客服处理!',
      name: 'lessonChangeActionFailDetail',
      desc: '',
      args: [],
    );
  }

  /// `更换超时`
  String get lessonChangeActionTimeout {
    return Intl.message(
      '更换超时',
      name: 'lessonChangeActionTimeout',
      desc: '',
      args: [],
    );
  }

  /// `返回学习页面`
  String get lessonChangeActionBackPlanHome {
    return Intl.message(
      '返回学习页面',
      name: 'lessonChangeActionBackPlanHome',
      desc: '',
      args: [],
    );
  }

  /// `排队更换中，请稍后回来查看新年级!`
  String get lessonChangeActionTimeoutDetail {
    return Intl.message(
      '排队更换中，请稍后回来查看新年级!',
      name: 'lessonChangeActionTimeoutDetail',
      desc: '',
      args: [],
    );
  }

  /// `待开课`
  String get waitStartClass {
    return Intl.message(
      '待开课',
      name: 'waitStartClass',
      desc: '',
      args: [],
    );
  }

  /// `课程即将开始`
  String get classStartingSoon {
    return Intl.message(
      '课程即将开始',
      name: 'classStartingSoon',
      desc: '',
      args: [],
    );
  }

  /// `先做好学习准备吧～`
  String get studyPrepGuideMessage {
    return Intl.message(
      '先做好学习准备吧～',
      name: 'studyPrepGuideMessage',
      desc: '',
      args: [],
    );
  }

  /// `学习已结束`
  String get studyEndMessage {
    return Intl.message(
      '学习已结束',
      name: 'studyEndMessage',
      desc: '',
      args: [],
    );
  }

  /// `等待开启新的学习计划哦～`
  String get waitOpenNewPlanMessage {
    return Intl.message(
      '等待开启新的学习计划哦～',
      name: 'waitOpenNewPlanMessage',
      desc: '',
      args: [],
    );
  }

  /// `去点亮`
  String get toLight {
    return Intl.message(
      '去点亮',
      name: 'toLight',
      desc: '',
      args: [],
    );
  }

  /// `我还要学`
  String get weAgingStudy {
    return Intl.message(
      '我还要学',
      name: 'weAgingStudy',
      desc: '',
      args: [],
    );
  }

  /// `已连接`
  String get youConnected {
    return Intl.message(
      '已连接',
      name: 'youConnected',
      desc: '',
      args: [],
    );
  }

  /// `我的学伴`
  String get myPartner {
    return Intl.message(
      '我的学伴',
      name: 'myPartner',
      desc: '',
      args: [],
    );
  }

  /// `动态`
  String get feed {
    return Intl.message(
      '动态',
      name: 'feed',
      desc: '',
      args: [],
    );
  }

  /// `学伴`
  String get partner {
    return Intl.message(
      '学伴',
      name: 'partner',
      desc: '',
      args: [],
    );
  }

  /// `送花花`
  String get sendFlower {
    return Intl.message(
      '送花花',
      name: 'sendFlower',
      desc: '',
      args: [],
    );
  }

  /// `已送出`
  String get alreadySend {
    return Intl.message(
      '已送出',
      name: 'alreadySend',
      desc: '',
      args: [],
    );
  }

  /// `戳一戳`
  String get poke {
    return Intl.message(
      '戳一戳',
      name: 'poke',
      desc: '',
      args: [],
    );
  }

  /// `已戳`
  String get alreadyPoked {
    return Intl.message(
      '已戳',
      name: 'alreadyPoked',
      desc: '',
      args: [],
    );
  }

  /// `发现学伴`
  String get findPartner {
    return Intl.message(
      '发现学伴',
      name: 'findPartner',
      desc: '',
      args: [],
    );
  }

  /// `累计学习{days}天`
  String studyDays(Object days) {
    return Intl.message(
      '累计学习$days天',
      name: 'studyDays',
      desc: '',
      args: [days],
    );
  }

  /// `添加学伴`
  String get addPartner {
    return Intl.message(
      '添加学伴',
      name: 'addPartner',
      desc: '',
      args: [],
    );
  }

  /// `等待同意`
  String get waitAgree {
    return Intl.message(
      '等待同意',
      name: 'waitAgree',
      desc: '',
      args: [],
    );
  }

  /// `去领课`
  String get goGetLesson {
    return Intl.message(
      '去领课',
      name: 'goGetLesson',
      desc: '',
      args: [],
    );
  }

  /// `去看看`
  String get goAndSee {
    return Intl.message(
      '去看看',
      name: 'goAndSee',
      desc: '',
      args: [],
    );
  }

  /// `已成为学伴`
  String get isMyPartner {
    return Intl.message(
      '已成为学伴',
      name: 'isMyPartner',
      desc: '',
      args: [],
    );
  }

  /// `还没有学伴，去添加一些吧`
  String get partnerEmpty {
    return Intl.message(
      '还没有学伴，去添加一些吧',
      name: 'partnerEmpty',
      desc: '',
      args: [],
    );
  }

  /// `暂时没有新动态哦`
  String get dynamicEmpty {
    return Intl.message(
      '暂时没有新动态哦',
      name: 'dynamicEmpty',
      desc: '',
      args: [],
    );
  }

  /// `你确认要解除学伴关系吗？`
  String get deletePartnerTitle {
    return Intl.message(
      '你确认要解除学伴关系吗？',
      name: 'deletePartnerTitle',
      desc: '',
      args: [],
    );
  }

  /// `如果想要恢复关系，需要对方再次通过`
  String get deletePartnerDetails {
    return Intl.message(
      '如果想要恢复关系，需要对方再次通过',
      name: 'deletePartnerDetails',
      desc: '',
      args: [],
    );
  }

  /// `再想想`
  String get deleteCancel {
    return Intl.message(
      '再想想',
      name: 'deleteCancel',
      desc: '',
      args: [],
    );
  }

  /// `确认解除`
  String get deleteConfirm {
    return Intl.message(
      '确认解除',
      name: 'deleteConfirm',
      desc: '',
      args: [],
    );
  }

  /// `点亮`
  String get lightUp {
    return Intl.message(
      '点亮',
      name: 'lightUp',
      desc: '',
      args: [],
    );
  }

  /// `共{count}个收到的花花`
  String getFlowers(Object count) {
    return Intl.message(
      '共$count个收到的花花',
      name: 'getFlowers',
      desc: '',
      args: [count],
    );
  }

  /// `知道了`
  String get konw {
    return Intl.message(
      '知道了',
      name: 'konw',
      desc: '',
      args: [],
    );
  }

  /// `等待通过`
  String get waitPass {
    return Intl.message(
      '等待通过',
      name: 'waitPass',
      desc: '',
      args: [],
    );
  }

  /// `成为学伴`
  String get beAStudyPartner {
    return Intl.message(
      '成为学伴',
      name: 'beAStudyPartner',
      desc: '',
      args: [],
    );
  }

  /// `互为学伴`
  String get mutualCompanionship {
    return Intl.message(
      '互为学伴',
      name: 'mutualCompanionship',
      desc: '',
      args: [],
    );
  }

  /// `消息`
  String get partnerMessage {
    return Intl.message(
      '消息',
      name: 'partnerMessage',
      desc: '',
      args: [],
    );
  }

  /// `综合统计`
  String get leanDataStatistics {
    return Intl.message(
      '综合统计',
      name: 'leanDataStatistics',
      desc: '',
      args: [],
    );
  }

  /// `成长记录`
  String get growthRecord {
    return Intl.message(
      '成长记录',
      name: 'growthRecord',
      desc: '',
      args: [],
    );
  }

  /// `兑换限制中`
  String get buyLock {
    return Intl.message(
      '兑换限制中',
      name: 'buyLock',
      desc: '',
      args: [],
    );
  }

  /// `扫码和我一起学`
  String get scanLearnWithMe {
    return Intl.message(
      '扫码和我一起学',
      name: 'scanLearnWithMe',
      desc: '',
      args: [],
    );
  }

  /// `我是`
  String get iAm {
    return Intl.message(
      '我是',
      name: 'iAm',
      desc: '',
      args: [],
    );
  }

  /// `去抽奖`
  String get goRaffle {
    return Intl.message(
      '去抽奖',
      name: 'goRaffle',
      desc: '',
      args: [],
    );
  }

  /// `去领取`
  String get getGift {
    return Intl.message(
      '去领取',
      name: 'getGift',
      desc: '',
      args: [],
    );
  }

  /// `去查看`
  String get goScan {
    return Intl.message(
      '去查看',
      name: 'goScan',
      desc: '',
      args: [],
    );
  }

  /// `去分享`
  String get goShare {
    return Intl.message(
      '去分享',
      name: 'goShare',
      desc: '',
      args: [],
    );
  }

  /// `去装扮`
  String get goToDress {
    return Intl.message(
      '去装扮',
      name: 'goToDress',
      desc: '',
      args: [],
    );
  }

  /// `加载中`
  String get videoLoading {
    return Intl.message(
      '加载中',
      name: 'videoLoading',
      desc: '',
      args: [],
    );
  }

  /// `还没有获得奖励哦`
  String get noGift {
    return Intl.message(
      '还没有获得奖励哦',
      name: 'noGift',
      desc: '',
      args: [],
    );
  }

  /// `查看物流`
  String get scanWlInfo {
    return Intl.message(
      '查看物流',
      name: 'scanWlInfo',
      desc: '',
      args: [],
    );
  }

  /// `我的奖励`
  String get myGift {
    return Intl.message(
      '我的奖励',
      name: 'myGift',
      desc: '',
      args: [],
    );
  }

  /// `查看我的奖励`
  String get scanMyGift {
    return Intl.message(
      '查看我的奖励',
      name: 'scanMyGift',
      desc: '',
      args: [],
    );
  }

  /// `下一个`
  String get next {
    return Intl.message(
      '下一个',
      name: 'next',
      desc: '',
      args: [],
    );
  }

  /// `立即解锁`
  String get unlockRights {
    return Intl.message(
      '立即解锁',
      name: 'unlockRights',
      desc: '',
      args: [],
    );
  }
}

class AppLocalizationDelegate extends LocalizationsDelegate<S> {
  const AppLocalizationDelegate();

  List<Locale> get supportedLocales {
    return const <Locale>[
      Locale.fromSubtags(languageCode: 'zh', scriptCode: 'Hans'),
    ];
  }

  @override
  bool isSupported(Locale locale) => _isSupported(locale);
  @override
  Future<S> load(Locale locale) => S.load(locale);
  @override
  bool shouldReload(AppLocalizationDelegate old) => false;

  bool _isSupported(Locale locale) {
    for (var supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == locale.languageCode) {
        return true;
      }
    }
    return false;
  }
}
